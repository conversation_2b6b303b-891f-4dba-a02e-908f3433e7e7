package com.workflowenginee.workflow.model;

import java.io.Serializable;
import java.util.Date;

public class Application implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String id;
    private String status = "PENDING";
    private String applicantEmail = "<EMAIL>";
    private String officerEmail = "<EMAIL>";
    private String companyName;
    private Date createdDate;
    
    // Getters and setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getApplicantEmail() {
        return applicantEmail;
    }
    
    public void setApplicantEmail(String applicantEmail) {
        this.applicantEmail = applicantEmail;
    }
    
    public String getOfficerEmail() {
        return officerEmail;
    }
    
    public void setOfficerEmail(String officerEmail) {
        this.officerEmail = officerEmail;
    }
    
    public String getCompanyName() {
        return companyName;
    }
    
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    
    public Date getCreatedDate() {
        return createdDate;
    }
    
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
}

