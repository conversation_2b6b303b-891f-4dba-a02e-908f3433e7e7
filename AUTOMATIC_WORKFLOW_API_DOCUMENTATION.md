# Automatic Complaint Lifecycle Workflow API Documentation

## ✅ **Fully Automatic Decision Making**

The workflow has been updated to make all decisions automatically based on complaint data, removing manual decision inputs.

## 🔄 **Automatic Workflow Flow**

### **BPMN Process Flow:**
```
Start → Fetch Complaint Details → Notify Agent → 
Intermediate Signal (signalNotifyAgent) → Fetch After Signal → 
Automatic Agent Decision → Gateway → [Close Path | Escalate Path]
```

### **Decision Logic:**
All decisions are made automatically by `FetchAfterSignalDelegate` based on complaint attributes:
- **Priority**: HIGH/URGENT → Escalate
- **Type**: POLICY_VIOLATION → Escalate  
- **Department**: MANAGEMENT → Escalate
- **Type**: COMPLEX_COMPLAINT, SERVICE_QUALITY, BILLING_DISPUTE → Escalate
- **Type**: SIMPLE_INQUIRY, INFORMATION_REQUEST, TECHNICAL_ISSUE → Close
- **Default**: Close

## 📋 **Updated API Endpoints**

### **1. Start Complaint Lifecycle**
```bash
POST /api/v1/workflow/start-complaint-lifecycle/{complaintId}
```

**Example:**
```bash
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-001
```

**Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle process started successfully",
  "processInstanceId": "3dca7aac-4b4d-11f0-be36-14857f938c25",
  "complaintId": "test-complaint-001"
}
```

### **2. Resume Complaint Lifecycle (Automatic)**
```bash
POST /api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/{signalType}
```

#### **Signal Types:**
- `NotifyAgent` - Triggers automatic agent decision
- `Escalated` - Triggers automatic escalation completion

### **3. Trigger Agent Decision (Automatic)**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-001"
}
```

**Automatic Flow Result:**
```
Signal → FetchAfterSignalDelegate (analyzes complaint data) → 
Automatic Decision (close/escalate) → Gateway → [Close Path | Escalate Path]
```

**Decision Examples:**
- **High Priority Complaint** → Automatic Escalation
- **Simple Inquiry** → Automatic Close
- **Policy Violation** → Automatic Escalation
- **Technical Issue** → Automatic Close

### **4. Complete Escalation (Automatic)**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/Escalated
Content-Type: application/json

{
  "complaintId": "test-complaint-001"
}
```

**Automatic Flow Result:**
```
Signal → FetchAfterEscalationDelegate → 
Agent Lead Close → Send Client Notification → End
```

### **5. Check Status**
```bash
GET /api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

**Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle status retrieved successfully",
  "processInstanceId": "3dca7aac-4b4d-11f0-be36-14857f938c25",
  "processDefinitionKey": "complaintLifecycleProcess",
  "activeActivities": ["signalNotifyAgent"],
  "variables": {
    "complaintId": "test-complaint-001",
    "complaintData": {
      "priority": "HIGH",
      "typeOfComplaint": "SERVICE_QUALITY",
      "department": "CUSTOMER_SERVICE"
    },
    "agentDecision": "escalate"
  },
  "isEnded": false
}
```

## 🎯 **Automatic Decision Logic**

### **FetchAfterSignalDelegate Decision Rules:**

```java
// High priority or urgent complaints → Escalate
if ("HIGH".equals(priority) || "URGENT".equals(priority)) {
    return "escalate";
}

// Policy violations → Escalate
if ("POLICY_VIOLATION".equals(typeOfComplaint)) {
    return "escalate";
}

// Management department → Escalate
if ("MANAGEMENT".equals(department)) {
    return "escalate";
}

// Complex complaints → Escalate
if ("COMPLEX_COMPLAINT".equals(typeOfComplaint) ||
    "SERVICE_QUALITY".equals(typeOfComplaint) ||
    "BILLING_DISPUTE".equals(typeOfComplaint)) {
    return "escalate";
}

// Simple issues → Close
if ("SIMPLE_INQUIRY".equals(typeOfComplaint) || 
    "INFORMATION_REQUEST".equals(typeOfComplaint) ||
    "TECHNICAL_ISSUE".equals(typeOfComplaint)) {
    return "close";
}

// Default → Close
return "close";
```

## 🧪 **Complete Testing Sequence**

### **Test 1: Automatic Close Path (Simple Inquiry)**
```bash
# Step 1: Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/simple-inquiry-001

# Step 2: Trigger automatic decision
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/NotifyAgent
{
  "complaintId": "simple-inquiry-001"
}

# Step 3: Check final status
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Expected: agentDecision = "close", process ended, clientNotified = true
```

### **Test 2: Automatic Escalation Path (High Priority)**
```bash
# Step 1: Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/high-priority-001

# Step 2: Trigger automatic decision
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/NotifyAgent
{
  "complaintId": "high-priority-001"
}

# Step 3: Check status (should be waiting at signalEscalated)
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Expected: agentDecision = "escalate", activeActivities = ["signalEscalated"]

# Step 4: Complete escalation automatically
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/Escalated
{
  "complaintId": "high-priority-001"
}

# Step 5: Check final status
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Expected: closedBy = "AGENT_LEAD", process ended, clientNotified = true
```

## 📊 **Process Variables (Automatic)**

### **Key Variables:**
- `complaintId`: Complaint identifier
- `complaintData`: Complete complaint information from workplace learning service
- `agentDecision`: Automatically determined ("close" or "escalate")
- `escalationDecision`: Always "close" for escalated complaints
- `closedBy`: "AGENT" or "AGENT_LEAD"
- `clientNotified`: Boolean indicating if client was notified

### **Decision Factors:**
- `priority`: HIGH, MEDIUM, LOW, URGENT
- `typeOfComplaint`: SIMPLE_INQUIRY, COMPLEX_COMPLAINT, POLICY_VIOLATION, etc.
- `department`: MANAGEMENT, CUSTOMER_SERVICE, TECHNICAL, etc.

## 📨 **Kafka Notifications (Automatic)**

### **Agent Notifications (IN_APP):**
- **Assignment**: When complaint assigned to agent
- **Escalation**: When complaint escalated to agent lead

### **Client Notifications (EMAIL):**
- **Resolution**: When agent closes complaint
- **Escalation Resolution**: When agent lead closes escalated complaint

## ✅ **Benefits of Automatic Implementation**

1. **✅ No Manual Input Required**: Decisions based purely on complaint data
2. **✅ Consistent Logic**: Same decision criteria applied every time
3. **✅ Faster Processing**: No waiting for manual decision input
4. **✅ Audit Trail**: Clear logging of automatic decision reasoning
5. **✅ Scalable**: Can process high volumes without human intervention

## 🚀 **Ready for Production**

**Status**: ✅ **Fully Automatic Workflow Ready**

**Features:**
- ✅ **Automatic Decision Making**: Based on complaint attributes
- ✅ **Intelligent Routing**: High priority → Escalate, Simple → Close
- ✅ **Complete Integration**: Workflow → Communication service
- ✅ **Professional Notifications**: Client email templates
- ✅ **Robust Fallbacks**: Handles missing data gracefully

**API Usage:**
- **Simple**: Only `complaintId` required in request body
- **Automatic**: No manual decision parameters needed
- **Intelligent**: System determines best path based on complaint data

The workflow now operates fully automatically, making intelligent decisions based on complaint characteristics!
