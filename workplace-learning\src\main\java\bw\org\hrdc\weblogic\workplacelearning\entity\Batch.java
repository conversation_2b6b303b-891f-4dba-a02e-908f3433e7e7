package bw.org.hrdc.weblogic.workplacelearning.entity;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "batch")
@Getter
@Setter
@ToString
public class Batch {

    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "uuid2")
    @Column(name = "id", columnDefinition = "uuid")
    private UUID id;


    @Column(name = "batch_no", unique = true, nullable = false)
    private String batchNo;
    
    @Column(name = "batch_name", nullable = false)
    private String batchName;

    @Column(name = "batch_type", nullable = false)
    private String batchType;

    @Enumerated(EnumType.STRING)
    @Column(name = "batch_status", nullable = false)
    private ApplicationStatus batchStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_status")
    private ApplicationStatus applicationStatus;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "batch_logs", columnDefinition = "jsonb")
    private Map<String, Object> batchLogs;

    @Column(name = "date_posted", nullable = false)
    private Date datePosted;
    
    @Column(name = "application_count", nullable = false)
    private int applicationCount;

    @Column(name = "action_taken_by", nullable = false)
    private String actionTakenBy;

    @Column(name = "user_id", nullable = false)
    private UUID userId;
    
    @PrePersist
    protected void onCreate() {
        if (batchNo == null) {
            // Generate batch number with prefix based on batch type
            String prefix = batchType != null ? batchType.toUpperCase() : "BATCH";
            String datePart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String randomPart = String.format("%04d", (int) (Math.random() * 10000));
            this.batchNo = prefix + "-" + datePart + "-" + randomPart;
        }
        
        if (batchName == null && batchNo != null) {
            // Create batch name as combination of date and number
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateStr = LocalDateTime.now().format(formatter);
            String batchTypeDisplay = batchType != null ? batchType.toUpperCase() + " " : "";
            this.batchName = batchTypeDisplay + "Batch " + dateStr + " #" + batchNo.substring(batchNo.lastIndexOf("-") + 1);
        }
        
        if (datePosted == null) {
            this.datePosted = new Date();
        }
    }
}
