package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintsStats;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.AuditLog;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.AuditLogRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.BaseSpecifications;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 07/04/25 02:17
 * @UpdatedBy martinspectre
 * @UpdatedOn 07/04/25 02:17
 */
@Service
@RequiredArgsConstructor
public class AppealService {
    private final ComplaintRepository appealRepository;
    private final AuditLogRepository auditLogRepository;

    private final PaperlessService paperlessService;
    private final ComplaintDocumentRepository documentRepository;
    @Autowired
    private CompanyClient companyClient;

    private String generateAppealReference() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String uniquePart = String.format("%04d", (int) (Math.random() * 10000));
        return "APP-" + datePart + "-" + uniquePart;
    }

    public ComplaintEntity createAppeal(ComplaintEntity appeal, String userId) {

        String referenceNumber = generateAppealReference();

        appeal.setStatus(Enums.ComplaintStatus.OPEN);
        appeal.setState(Enums.ComplaintState.SUBMITTED);
        appeal.setReferenceNumber(referenceNumber);

        ComplaintEntity savedAppeal = appealRepository.save(appeal);

        AuditLog log = new AuditLog(savedAppeal, "CREATED", "Appeal submitted", userId, LocalDateTime.now());
        auditLogRepository.save(log);

        return savedAppeal;
    }

    public ComplaintEntity fetchById(String appealId) {
        return appealRepository.findByUuid(appealId)
                .orElseThrow(() -> new RuntimeException("Appeal not found"));
    }

    public ComplaintResponse fetchDetailedAppeal(String appealId){
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Optional<ComplaintEntity> appeal = appealRepository.findByUuid(appealId);

        if(appeal.isPresent()){

            ComplaintEntity appealData = appeal.get();
            ComplaintResponse appealResponse = new ComplaintResponse();

            appealResponse.setUuid(appealData.getUuid());
            appealResponse.setReference(appealData.getReference());
            appealResponse.setReferenceNumber(appealData.getReferenceNumber());
            appealResponse.setOrganisationId(appealData.getOrganisationId());
            appealResponse.setDepartment(appealData.getDepartment());
            appealResponse.setTypeOfComplaint(appealData.getTypeOfComplaint());
            appealResponse.setState(appealData.getState());
            appealResponse.setStatus(appealData.getStatus());
            appealResponse.setCreatedAt(appealData.getCreatedAt());
            appealResponse.setUpdatedAt(appealData.getUpdatedAt());
            appealResponse.setCreatedBy(appealData.getCreatedBy());
            appealResponse.setUpdatedBy(appealData.getUpdatedBy());
            appealResponse.setAssignedTo(appealData.getAssignee());
            appealResponse.setDocuments(appealData.getDocuments());
            appealResponse.setComments(appealData.getComments());
            appealResponse.setAuditLogs(appealData.getAuditLogs());
            appealResponse.setDescription(appealData.getDescription());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(appealData.getOrganisationId())).toList();

            if(!company.isEmpty()){
                appealResponse.setOrganisation(company.get(0).get("name").toString());
            }

            return appealResponse;
        }
        return null;
    }

    public Page<ListData> fetchList(PageRequest pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity updateStatus(String appealId, String userId, Enums.ComplaintStatus status, Enums.ComplaintState state) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            // Validate transition
            validateWorkflowTransition(appeal.getStatus(), appeal.getState(), status, state);

            appeal.setStatus(status);
            appeal.setState(state);
            appealRepository.save(appeal);

            AuditLog log = new AuditLog(appeal, "STATUS_CHANGED", "Status changed to " + status, userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return appeal;
    }

    @Transactional()
    public void  createAppealDocument(ComplaintDocument appealDocument) {
        documentRepository.save(appealDocument);
    }

    private void validateWorkflowTransition(Enums.ComplaintStatus currentStatus, Enums.ComplaintState currentState,
                                            Enums.ComplaintStatus newStatus, Enums.ComplaintState newState) {
        if (currentStatus == Enums.ComplaintStatus.OPEN && currentState == Enums.ComplaintState.SUBMITTED) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
        }
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK) return;
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.ESCALATED) return;
        }
        if (currentStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS) return;
        }
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.ESCALATED) {
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

        throw new IllegalArgumentException("Invalid status/state transition");
    }

    public Page<ListData> fetchCompanyList(String companyId, Pageable pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("organisationId"), companyId));

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals =  appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintsStats getCompanyStatistics(String companyId) {
        List<Object[]> results = appealRepository.getCompanyComplaintStatistics(companyId, Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public ComplaintsStats getStatistics() {
        List<Object[]> results = appealRepository.getComplaintStatistics(Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public Page<ListData> fetchUserAssignedList(PageRequest pageable, ComplaintSearchCriteria searchCriteria) {

        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());
        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("status"), Enums.ComplaintStatus.valueOf(searchCriteria.getStatus().toUpperCase())));
        }
        if (searchCriteria.getAssignedTo() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("assignee"), searchCriteria.getAssignedTo()));
        }

        if (searchCriteria.getComplaintStates() != null && !searchCriteria.getComplaintStates().isEmpty()) {
            List<Enums.ComplaintState> states = searchCriteria.getComplaintStates();
            spec = spec.and((root, query, criteriaBuilder) -> root.get("state").in(states));
        }

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, pageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity assignedNewAssistance(String appealId, String  agentId, String userId) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            appeal.setAssignee(agentId);
            appealRepository.save(appeal);

            AuditLog log = new AuditLog(appeal, "ASSIGN_AGENT_TO_COMPLAINT", "Appeal assigned to human agent ", userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return appeal;
    }

}
