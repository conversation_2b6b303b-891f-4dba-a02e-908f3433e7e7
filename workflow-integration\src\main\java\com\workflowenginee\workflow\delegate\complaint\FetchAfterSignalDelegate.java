package com.workflowenginee.workflow.delegate.complaint;

import java.util.HashMap;
import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Delegate to fetch complaint details after signal event
 */
@Component("fetchAfterSignalDelegate")
@Slf4j
public class FetchAfterSignalDelegate implements JavaDelegate {

    @Autowired
    private WorkplaceLearningClient workplaceLearningClient;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");

        log.info("[Process: {}] Fetching complaint details after signal for complaint ID: {}", processInstanceId, complaintId);

        try {
            // Fetch updated complaint details from workplace learning service
            ApiResponse<?> response = workplaceLearningClient.getComplaintById(complaintId);

            if (response.isStatus() && response.getData() != null) {
                // Store updated complaint data in process variables
                Map<String, Object> complaintData = (Map<String, Object>) response.getData();
                execution.setVariable("complaintData", complaintData);
                execution.setVariable("complaintStatus", complaintData.get("status"));
                execution.setVariable("complaintState", complaintData.get("state"));
                execution.setVariable("assignee", complaintData.get("assignee"));
                execution.setVariable("organisationId", complaintData.get("organisationId"));
                execution.setVariable("referenceNumber", complaintData.get("referenceNumber"));
                execution.setVariable("priority", complaintData.get("priority"));
                execution.setVariable("typeOfComplaint", complaintData.get("typeOfComplaint"));
                execution.setVariable("department", complaintData.get("department"));

                // Check if manual decision was provided from controller
                String manualDecision = (String) execution.getVariable("manualDecision");
                String agentDecision;

                if (manualDecision != null && ("close".equals(manualDecision) || "escalate".equals(manualDecision))) {
                    // Use manual decision from controller
                    agentDecision = manualDecision;
                    log.info("[Process: {}] Using manual decision from controller: {}", processInstanceId, agentDecision);
                } else {
                    // Determine agent decision based on complaint data
                    agentDecision = determineAgentDecision(complaintData);
                    log.info("[Process: {}] Determined agent decision from complaint data: {}", processInstanceId, agentDecision);
                }

                execution.setVariable("agentDecision", agentDecision);

                log.info("[Process: {}] Successfully fetched updated complaint data. Agent decision: {}", processInstanceId, agentDecision);
            } else {
                log.warn("[Process: {}] Failed to fetch updated complaint data for ID: {}", processInstanceId, complaintId);
                
                // Use existing data if available, otherwise create fallback
                Map<String, Object> existingData = (Map<String, Object>) execution.getVariable("complaintData");
                if (existingData == null) {
                    Map<String, Object> fallbackData = new HashMap<>();
                    fallbackData.put("id", complaintId);
                    fallbackData.put("status", "OPEN");
                    fallbackData.put("state", "SUBMITTED");
                    fallbackData.put("message", "Updated complaint data not available - using fallback");
                    
                    execution.setVariable("complaintData", fallbackData);
                    execution.setVariable("complaintStatus", "OPEN");
                    execution.setVariable("complaintState", "SUBMITTED");
                }
                
                // Check for manual decision even when data is not available
                String manualDecision = (String) execution.getVariable("manualDecision");
                String agentDecision;

                if (manualDecision != null && ("close".equals(manualDecision) || "escalate".equals(manualDecision))) {
                    agentDecision = manualDecision;
                    log.info("[Process: {}] Using manual decision (no data available): {}", processInstanceId, agentDecision);
                } else {
                    agentDecision = "close"; // Default to close instead of chat
                    log.info("[Process: {}] Using default decision (no data available): {}", processInstanceId, agentDecision);
                }

                execution.setVariable("agentDecision", agentDecision);
            }

        } catch (Exception e) {
            log.error("[Process: {}] Error fetching updated complaint details for ID: {}", processInstanceId, complaintId, e);
            
            // Set error variables but don't fail the process
            execution.setVariable("fetchAfterSignalError", true);
            execution.setVariable("fetchAfterSignalErrorMessage", e.getMessage());
            
            // Check for manual decision even on error
            String manualDecision = (String) execution.getVariable("manualDecision");
            String agentDecision;

            if (manualDecision != null && ("close".equals(manualDecision) || "escalate".equals(manualDecision))) {
                agentDecision = manualDecision;
                log.info("[Process: {}] Using manual decision (error case): {}", processInstanceId, agentDecision);
            } else {
                agentDecision = "close"; // Default to close instead of chat
                log.info("[Process: {}] Using default decision (error case): {}", processInstanceId, agentDecision);
            }

            execution.setVariable("agentDecision", agentDecision);
        }
    }

    /**
     * Determines the agent decision based on complaint data
     */
    private String determineAgentDecision(Map<String, Object> complaintData) {
        String typeOfComplaint = (String) complaintData.get("typeOfComplaint");
        String priority = (String) complaintData.get("priority");
        String department = (String) complaintData.get("department");
        String status = (String) complaintData.get("status");
        
        // High priority or urgent complaints should be escalated
        if ("HIGH".equals(priority) || "URGENT".equals(priority)) {
            return "escalate";
        }
        
        // Policy violations need escalation
        if ("POLICY_VIOLATION".equals(typeOfComplaint)) {
            return "escalate";
        }
        
        // Management department complaints need escalation
        if ("MANAGEMENT".equals(department)) {
            return "escalate";
        }
        
        // Simple inquiries can be resolved by agent
        if ("SIMPLE_INQUIRY".equals(typeOfComplaint) || "INFORMATION_REQUEST".equals(typeOfComplaint)) {
            return "close";
        }

        // Technical issues might be resolved quickly
        if ("TECHNICAL_ISSUE".equals(typeOfComplaint)) {
            return "close";
        }

        // Complex complaints need escalation
        if ("COMPLEX_COMPLAINT".equals(typeOfComplaint) ||
            "SERVICE_QUALITY".equals(typeOfComplaint) ||
            "BILLING_DISPUTE".equals(typeOfComplaint)) {
            return "escalate";
        }

        // Default to close for most cases
        return "close";
    }
}
