spring.application.name=dms
# Default profile
spring.profiles.active=dev
#
# App Config
app.build.name=hrdc
app.build.version=0.0.1-SNAPSHOT
app.build.date=@build.timestamp@
#
# PostgreSQL Configuration
spring.datasource.url=************************************
spring.datasource.username=dms
spring.datasource.password=dms
spring.jpa.hibernate.ddl-auto=update

# Paperless-ngx API URL and Key
paperless.api.url=http://localhost:8000/api
paperless.api.key=your_paperless_api_key
paperless.api.auth.username=paperless
paperless.api.auth.password=paperles@123

