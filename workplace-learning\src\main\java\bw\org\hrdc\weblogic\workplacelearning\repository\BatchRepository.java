package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.Batch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * Repository for Batch entity.
 * Handles database operations for batch processing records.
 */
@Repository
public interface BatchRepository extends JpaRepository<Batch, UUID> {
    // Basic CRUD operations inherited from JpaRepository
}
