package bw.org.hrdc.weblogic.workplacelearning.constants;

/**
 * Enum for defining APPLICATION STATUSES FOR NCBSC APPLICATION.
 * Created by <PERSON><PERSON><PERSON> on 27/12/2024
 */
public enum ApplicationStatus {

    DRAFT, //Applicant did not submit
    SUBMITTED, //Applicant submitted, not yet assigned for assessment
    IN_PROCESSING, //Assigned to Agent for vetting
    IN_REVIEW, // Assigned to officer, under vetting
    IN_APPROVAL, //Assigned to manager for approval

    INITIAL, //Not yet submitted, still in draft state
    PENDING, //Not in draft and has been submitted but not yet acted upon
    APPROVED, //Accepted by the manager and the application is closed
    REJECTED, //Did not meet requirements
    CHANGE_REQUEST, //Changes requested
    PRE_APPROVED, //Approved by the officer/officer lead under review state
    EXPIRED, //Still in draft, Time allowed passed before it was submitted
    CANCELLED, //Still under draft/ submitted but was not assigned Closed while still awaiting processing
    
    PROCESSING, //Batch is currently being processed
    PROCESSED, //Batch processing completed successfully for all items
    PARTIALLY_PROCESSED //Batch processing completed with some failures
}
