server:
  port: 8089

spring:
  application:
    name: communication
  kafka:
    bootstrap-servers: ${KAFKA_SERVER:localhost:9094}
    consumer:
      group-id: email-service-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: sathaihakoajxbsy
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    protocol: smtp

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true
    sampler:
      probability: 1
  zipkin:
    base-url: ${ZIPKIN_BASE_URL:http://localhost:9411/}

info:
  app:
    name: "communication"
    description: "HRDC communication Application"
    version: "1.0.0"

eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}
