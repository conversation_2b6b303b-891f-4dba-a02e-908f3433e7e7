
package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.CompanyClient;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationService;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;


@Component("notifyLeadDelegate")
public class NotifyLeadDelegate implements JavaDelegate {

    private final CompanyClient companyClient;
    private final NotificationService notificationService;
    public NotifyLeadDelegate(CompanyClient companyClient,NotificationService notificationService) {
        this.companyClient = companyClient;
        this.notificationService = notificationService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String role = (String) execution.getVariable("role");
        String companyId = (String) execution.getVariable("companyId");
        String applicationType = (String) execution.getVariable("applicationType");

        System.out.println("[Process: " + processInstanceId + "] Starting email notification process");
        System.out.println("companyId: " + companyId);

        Map<String, Object> applicationData;
        try {
            applicationData = (Map<String, Object>) execution.getVariable("ApplicationData");
        } catch (ClassCastException e) {
            System.err.println("[Process: " + processInstanceId + "] Error casting ApplicationData to Map: " + e.getMessage());
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "Invalid application data format");
            return;
        }

        if (applicationData == null || !applicationData.containsKey("application")) {
            System.err.println("[Process: " + processInstanceId + "] No application data found to send email.");
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "No application data found");
            return;
        }

        Map<String, Object> application;
        try {
            application = (Map<String, Object>) applicationData.get("application");
        } catch (ClassCastException e) {
            System.err.println("[Process: " + processInstanceId + "] Error casting 'application' object: " + e.getMessage());
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "Invalid application structure");
            return;
        }

        try {
            String applicationId = String.valueOf(application.get("id"));
            String referenceNumber = String.valueOf(application.get("referenceNumber"));
            String applicationStatus = String.valueOf(application.get("applicationStatus"));
            String applicationState = String.valueOf(application.get("applicationState"));

            System.out.println("[Process: " + processInstanceId + "] Preparing email for application: " + applicationId);
            System.out.println("[Process: " + processInstanceId + "] Reference Number: " + referenceNumber);
            System.out.println("[Process: " + processInstanceId + "] Application Status: " + applicationStatus);

            String emailSubject = "NCBSC Application " + referenceNumber + " - Status: " + applicationStatus;
            String emailBody = "Application ID: " + applicationId + "\n"
                            + "Reference Number: " + referenceNumber + "\n"
                            + "Status: " + applicationStatus + "\n\n"
                            + "This is an automated notification about the application's status update.";

            System.out.println("[Process: " + processInstanceId + "] Email Subject: " + emailSubject);
            System.out.println("[Process: " + processInstanceId + "] Email Body:\n" + emailBody);

            // Store in process variables
            execution.setVariable("emailSubject", emailSubject);
            execution.setVariable("emailBody", emailBody);
            execution.setVariable("emailSent", true);

             // Create shared DTO
            NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .companyId(companyId)
                .applicationState(applicationState)
                .applicationType(applicationType)
                .build();
                
            // Notification logic
            String currentActivityId = execution.getCurrentActivityId();

            if (Enums.Role.AGENT.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(Enums.Role.OFFICER_LEAD.name())
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .applicationType(applicationType)
                .build(); 

                notificationService.notifyUsersByRole(notifyUsersByRoleDto);
                // notificationService.notifyToClient(referenceNumber, applicationId, applicationStatus, companyId, applicationState, applicationType);

                notificationService.notifyToClient(notifyclientDto);

            } else if (Enums.Role.AGENT_LEAD.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(Enums.Role.AGENT_LEAD.name())
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .applicationType(applicationType)
                .build(); 
                notificationService.notifyUsersByRole(notifyUsersByRoleDto);
                notificationService.notifyToClient(notifyclientDto);

            } else if (Enums.Role.OFFICER.name().equalsIgnoreCase(role)|| "serviceTask_Notifymanager".equalsIgnoreCase(currentActivityId)) {

                System.out.println("[Process: " + processInstanceId + "] Officer approved - notifying manager");
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(Enums.Role.MANAGER.name())
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .applicationType(applicationType)
                .build(); 
                notificationService.notifyUsersByRole(notifyUsersByRoleDto);

                NotifyToClientDto notifyclient = NotifyToClientDto.builder()
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .companyId(companyId)
                .applicationState(applicationState)
                .applicationType(applicationType)
                .build();
                notificationService.notifyToClient(notifyclient);

            } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role)) {
                System.out.println("[Process: " + processInstanceId + "] Manager approved - notifying client");
                notificationService.notifyToClient(notifyclientDto);
            }

            System.out.println("[Process: " + processInstanceId + "] Notification flow completed.");

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error during email or notification processing: " + e.getMessage());
            e.printStackTrace();
            // execution.setVariable("emailSent", false);
            // execution.setVariable("emailError", e.getMessage());
        }
    }

}

