package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("closeComplaintDelegate")
public class CloseComplaintDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String assignee = (String) execution.getVariable("assignee");
            String closureReason = (String) execution.getVariable("closureReason");
            String closureComments = (String) execution.getVariable("closureComments");
            
            log.info("Agent {} closing complaint ID: {}", assignee, complaintId);

            // Prepare closure payload
            Map<String, Object> closurePayload = new HashMap<>();
            closurePayload.put("complaintId", complaintId);
            closurePayload.put("action", "CLOSE");
            closurePayload.put("performedBy", assignee);
            closurePayload.put("status", "CLOSED");
            closurePayload.put("state", "RESOLVED");
            closurePayload.put("closureReason", closureReason != null ? closureReason : "Resolved by agent");
            closurePayload.put("closureComments", closureComments);
            closurePayload.put("closedBy", "AGENT");

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(closurePayload, headers);

            // Close complaint in workplace learning service
            String closeUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/close";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                closeUrl, 
                HttpMethod.PUT, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully closed complaint {} by agent {}", complaintId, assignee);
                
                execution.setVariable("complaintClosed", true);
                execution.setVariable("closedBy", "AGENT");
                execution.setVariable("lastAction", "CLOSED_BY_AGENT");
                execution.setVariable("closureTimestamp", System.currentTimeMillis());
                
                // Send closure notification to complainant
                sendClosureNotification(execution, complaintId, assignee, "AGENT");
                
            } else {
                log.error("Failed to close complaint {} by agent {}", complaintId, assignee);
                throw new RuntimeException("Failed to close complaint");
            }

        } catch (Exception e) {
            log.error("Error closing complaint: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to close complaint: " + e.getMessage());
            throw new RuntimeException("Error in CloseComplaintDelegate", e);
        }
    }

    private void sendClosureNotification(DelegateExecution execution, String complaintId, String closedBy, String closedByRole) {
        try {
            String organisationId = (String) execution.getVariable("organisationId");
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("organisationId", organisationId);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("action", "CLOSED");
            notificationPayload.put("closedBy", closedBy);
            notificationPayload.put("closedByRole", closedByRole);
            notificationPayload.put("message", "Your complaint has been resolved and closed");
            notificationPayload.put("type", "COMPLAINT_CLOSURE");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            restTemplate.exchange(notificationUrl, HttpMethod.POST, entity, Map.class);
            
            log.info("Closure notification sent for complaint {}", complaintId);
            
        } catch (Exception e) {
            log.warn("Failed to send closure notification for complaint {}: {}", complaintId, e.getMessage());
        }
    }
}
