package com.workflowenginee.workflow.api;

import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.util.ApiResponse;

@Component
public class CompanyClientFallback implements CompanyClient {

    @Override
    public ApiResponse<?> fetchAllActiveUsers(String role) {
        return new ApiResponse<>(
            false,
            "Active users data not reachable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> fetchCompanyById(String companyId) {
        return new ApiResponse<>(
            false,
            "Company data not reachable",
            null,
            null
        );
    }
    
    @Override
    public ApiResponse<?> fetchUserById(String userId) {
        return new ApiResponse<>(
            false,
            "User data not reachable",
            null,
            null
        );
    }

}
