package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.ResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ApplicationStatusUpdatePayload;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanChangeRequestDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanChangeRequest;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.service.trainingPlan.TrainingPlanCRService;
import bw.org.hrdc.weblogic.workplacelearning.service.WorkPlaceTrainingPlanCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

@RestController
@RequestMapping(path = "/api/v1/workplace-learning/training-plans/{planId}/change-requests", produces = {MediaType.APPLICATION_JSON_VALUE})
public class WorkPlaceTrainingPlanChangeRequestController {

    private static final Logger logger = LoggerFactory.getLogger(WorkPlaceTrainingPlanChangeRequestController.class);

    private final TrainingPlanCRService service;

    private final WorkPlaceTrainingPlanCommentsService commentsService;
    private final WorkflowClient workflowClient;
    private final NotificationService notificationService;

    public WorkPlaceTrainingPlanChangeRequestController(TrainingPlanCRService service,
                                           WorkPlaceTrainingPlanCommentsService commentsService, WorkflowClient workflowClient, NotificationService notificationService) {
        this.notificationService = notificationService;
        this.service = service;
        this.commentsService = commentsService;
        this.workflowClient = workflowClient;
    }

    @PostMapping("/")
    @Transactional
    public ResponseEntity<?> create(@RequestBody WorkPlaceTrainingPlanChangeRequestDto application) {
        logger.info("Training Plan CR Application creation initiated");
        try {
            String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("app");
            application.setApplicationNumber(applicationNumber);
            if(application.getApplicationState().equalsIgnoreCase(Enums.State.SUBMITTED.name())){
                String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                application.setReferenceNumber(referenceNumber);
                application.setSubmissionDate(LocalDate.now());
            }

            WorkPlaceTrainingPlanChangeRequest crApplication = service.create(application);

            if (crApplication == null) {
                logger.error("New Training Plan CR application for company id {} failed to create", application.getOrganisationId());
                return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create Training Plan CR application.");
            }

            logger.info("New Training plan CR application for company id {} created successfully", application.getOrganisationId());

            return ResponseEntity.ok(new ApiResponse<>(true, "Training Plan CR Application created successfully", crApplication, null));
        }catch (Exception exception){
            logger.error("Training Plan CR Application for company id {} failed with exception: {}", application.getOrganisationId(), exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

    @PutMapping("/{id}")
    @Transactional
    public ResponseEntity<?> update(@PathVariable UUID id, @RequestBody WorkPlaceTrainingPlanChangeRequest application) {
        logger.info("Training Plan CR Application update initiated");
        try {
            if(application.getApplicationState().equalsIgnoreCase(Enums.State.SUBMITTED.name()) && application.getReferenceNumber().isEmpty()){
                String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                application.setReferenceNumber(referenceNumber);

                application.setSubmissionDate(LocalDate.now());
            }


            Optional<WorkPlaceTrainingPlanChangeRequest> crApplication = service.update(id, application);

            if (crApplication.isEmpty()) {
                logger.error("Training Plan CR application for company id {} failed to update", application.getOrganisationId());
                return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create Training Plan CR application.");
            }

            logger.info("Training plan CR application for company id {} updated successfully", application.getOrganisationId());

            return ResponseEntity.ok(new ApiResponse<>(true, "Training Plan CR Application created successfully", crApplication, null));
        }catch (Exception exception){
            logger.error("Training Plan CR Application update for company id {} failed with exception: {}", application.getOrganisationId(), exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

    @GetMapping("/")
    public ResponseEntity<ApiResponse<Map<String, Object>>> fetchRecords(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date submissionDate,
            @RequestParam(required = false) UUID userId,
            @RequestParam(required = false) UUID organisationId,
            @RequestParam(required = false) String applicationStatus,
            @RequestParam(required = false) String applicationState,
            @RequestParam(required = false) String programme,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String referenceNumber,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) String role,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "100") int size) {

        logger.info("Fetching training plans cr with criteria - role: {}, userId: {}, organisationId: {}, status: {}, programme: {}, referenceNumber: {}, search: {}",
                role, userId, organisationId, applicationStatus, programme, referenceNumber, search);

        try {
            if (search != null && search.contains(":")) {
                String[] parts = search.split(":", 2);
                if (parts.length == 2) {
                    applicationStatus = parts[0].trim().toUpperCase();
                    applicationState = parts[1].trim().toUpperCase();
                    logger.info("Extracted from search parameter: status={}, state={}", applicationStatus, applicationState);
                    search = null;
                }
            }

            Map<String, Object> response = service.getTrainingPlansByRole(
                    role, userId, organisationId, applicationStatus, applicationState, submissionDate,
                    startDate, endDate, search, referenceNumber, companyName,
                    pageNumber, size);

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            logger.error("Failed to fetch training plans cr with exception: ", e);
            String errorMessage = String.format("Failed to fetch training plans cr : %s", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, errorMessage, null, null));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getApplicationById(@PathVariable UUID id) {
        try {
            WorkPlaceTrainingPlanChangeRequest application = service.getTrainingPlan(id);
            if(application != null) {
                Map<String, Object> response = new HashMap<>();

                response.put("application", application);

                List<WorkPlaceTrainingPlanComments> comments = commentsService.getAuditLogsForTrainingPlan(id);
                if(!comments.isEmpty()) {
                    List<Map<String, Object>> commentDtos = comments.stream()
                            .map(comment -> {
                                Map<String, Object> dto = new HashMap<>();
                                dto.put("id", comment.getId());
                                dto.put("trainingPlanId", comment.getTrainingPlan().getId());
                                dto.put("action", comment.getAction());
                                dto.put("comments", comment.getComments());
                                dto.put("updatedBy", comment.getUpdatedBy());
                                dto.put("timestamp", comment.getTimestamp());
                                return dto;
                            })
                            .collect(Collectors.toList());
                    response.put("comments", commentDtos);
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", response, null));
            } else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to fetch training plan cr with ID {} with exception: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteRecord(@PathVariable UUID id) {
        try {
            service.softDeleteTrainingPlan(id);
            return ResponseEntity.ok(new ApiResponse<>(true, "Record deleted successfully", null, null));

        } catch (ApplicationNotFoundException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errorCode", "RESOURCE_NOT_FOUND");
            errorResponse.put("errorMessage", e.getMessage());

            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (Exception e) {
            logger.error("Failed to delete training plan cr with ID {} with exception: {}", id, e.getMessage());

            return getInternalServerError("Internal server error occurred");
        }
    }

    @PutMapping("/{id}/assign-user")
    public ResponseEntity<ResponseDto> assignAgent(@PathVariable UUID id, @RequestBody Map<String, Object> payload) {
        logger.info("Training plan CR user assignment initiated for training plan ID: {}", id);
        try {
            String role = payload.get("role").toString();
            String userId = payload.get("userId").toString();

            if(role.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                                "Required fields are missing, please provide user id and their respective role"));
            }

            WorkPlaceTrainingPlanChangeRequest trainingPlan = service.getTrainingPlan(id);

            if (trainingPlan != null) {
                int updatedResult = service.updateTrainingPlanAssignedUser(id, Enums.UserRoles.valueOf(role), userId);
                if (updatedResult == 0) {
                    logger.error("Failed to update training plan CR ID: {}", id);
                    return ResponseEntity.badRequest()
                            .body(new ResponseDto("TRAINING_PLAN_ERROR_CR", "Failed to process training plan CR."));
                } else {
                    //TODO trigger notification to agent for the assignment made

                    if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                        notificationService.sendNotificationToUser(
                                userId,
                                trainingPlan.getReferenceNumber(),
                                trainingPlan.getId().toString(),
                                trainingPlan.getApplicationStatus(),
                                Enums.NotificationType.IN_APP.name(),
                                Enums.ApplicationType.WORK_SKILLS.name()

                        );
                    } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                        notificationService.sendNotificationToUser(
                                userId,
                                trainingPlan.getReferenceNumber(),
                                trainingPlan.getId().toString(),
                                trainingPlan.getApplicationStatus(),
                                Enums.NotificationType.IN_APP.name(),
                                Enums.ApplicationType.WORK_SKILLS.name()
                        );
                    }
                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Training plan CR assigned successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ResponseDto("TRAINING_PLAN_NOT_FOUND",
                                "Provided training plan CR identifier does not exist"));
            }
        } catch (Exception e) {
            logger.error("Failed to assign training plan CR of ID {} with exception: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }

    @PutMapping("/{id}/status-update")
    public ResponseEntity<ResponseDto> changeTrainingPlanStatus(
            @PathVariable UUID id,
            @RequestBody ApplicationStatusUpdatePayload payload) {
        logger.info("Training plan CR status update initiated for training plan ID: {}", id);
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();

            String actionType = null;

            if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                actionType = "Agent_action";
            }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                actionType = "Officer_action";
            }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)) {
                actionType = "Manager_action";
            }

            if(action.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                                "Required fields are missing, please provide user id and action"));
            }

            WorkPlaceTrainingPlanChangeRequest application = service.getTrainingPlan(id);
            if(application != null) {
                int updatedResult = service.updateTrainingPlanStatus(id, role, action, newAssignee);
                if (updatedResult == 0) {
                    logger.error("Failed to update status of training plan CR ID: {}", id);
                    return ResponseEntity.badRequest()
                            .body(new ResponseDto("TRAINING_PLAN_ERROR_CR", "Failed to process training plan."));
                } else {
                    // Create and save comments if provided
                    if (comments != null && !comments.isEmpty()) {
//                        WorkPlaceTrainingPlanComments logEntry = new WorkPlaceTrainingPlanComments();
//                        logEntry.setTrainingPlan(application);
//                        logEntry.setAction(action);
//                        logEntry.setComments(service.sanitizeHtml(comments));
//                        logEntry.setUpdatedBy(userId);
//                        logEntry.setTimestamp(LocalDateTime.now());
//                        commentsService.createComments(logEntry);
                    }

                    String processInstanceId = application.getProcessInstanceId();

                    if (processInstanceId != null && !processInstanceId.isEmpty()) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("referenceNumber", application.getReferenceNumber());
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.WORK_SKILLS.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, application.getReferenceNumber());
                            workflowClient.resumeProcess(processInstanceId, actionType, workflowPayload);
                        } catch (Exception e) {
                            // Log the error but continue with the application status update
                            logger.error("Failed to resume workflow process: {}", e.getMessage());
                            // Don't rethrow the exception - allow the application status update to succeed
                        }
                    } else {
                        logger.warn("No process instance ID found for application {}", application.getReferenceNumber());
                    }

                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Training plan CR status updated successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ResponseDto("TRAINING_PLAN_NOT_FOUND",
                                "Provided training plan identifier does not exist"));
            }
        } catch (Exception e) {
            logger.error("Failed to update status of training plan CR ID: {} with exception: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }
}
