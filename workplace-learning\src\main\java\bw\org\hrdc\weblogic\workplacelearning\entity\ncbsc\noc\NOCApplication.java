package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Assignment;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseContentDelivery;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseDeliverySchedule;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.converters.CourseContentDeliveryConverter;
import bw.org.hrdc.weblogic.workplacelearning.util.converters.CourseDeliveryScheduleConverter;
import bw.org.hrdc.weblogic.workplacelearning.util.converters.ScopeOfAccreditationConverter;
import bw.org.hrdc.weblogic.workplacelearning.util.converters.ShortCourseInformationConverter;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 09:06
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 09:06
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "ncbsc_change_request")
@Getter
@Setter
@NoArgsConstructor
public class NOCApplication extends Assignment implements Serializable {
    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "application_number")
    private String applicationNumber;

    @Column(name = "recognition_number")
    private String recognitionNumber;

    @Column(name = "organisation_id", nullable = false)
    private String organisationId;

    @Column(name = "assessment_purpose")
    private String trainingNeedsAssessmentPurpose;

    @Column(name = "skills_needs_analysis")
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;

    @Column(name = "short_course_delivery_mode")
    private String shortCourseDeliveryMode;

    @Column(name = "key_facilitation")
    private String keyFacilitation;

    @Column(name = "skills_assessment")
    private String assessmentType;

    @Column(name = "certification_assessment")
    private String certification;

    @Column(name = "third_party_arrangements")
    private String thirdPartyArrangements;

    @Column(name = "resources")
    private String resources;

    @Column(name = "short_course_endorsement")
    private String shortCourseEndorsement;

    @Column(name = "justification")
    private String justification;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_submitted", nullable = false)
    private LocalDateTime dateSubmitted = LocalDateTime.now();

    @Enumerated(EnumType.STRING)
    @Column(name = "application_status", nullable = false)
    private Enums.Status applicationStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_state", nullable = false)
    private Enums.State applicationState = Enums.State.DRAFT;

    @Enumerated(EnumType.STRING)
    private Enums.ChangeSeverity severity;

    @Column(name = "merged")
    private boolean merged = false;

    @Column(name = "is_major_change")
    private Boolean isMajorChange = false;

    @Column(name = "short_course_information", columnDefinition = "jsonb")
    @Convert(converter = ShortCourseInformationConverter.class)
    @ColumnTransformer(write = "?::jsonb")
    private ShortCourseInformation shortCourseInformation;

    @Column(name = "course_content_and_delivery", columnDefinition = "jsonb")
    @Convert(converter = CourseContentDeliveryConverter.class)
    @ColumnTransformer(write = "?::jsonb")
    private CourseContentDeliveryJson courseContentAndDelivery;

    @Column(name = "scope_of_accreditation", columnDefinition = "jsonb")
    @Convert(converter = ScopeOfAccreditationConverter.class)
    @ColumnTransformer(write = "?::jsonb")
    private Set<ScopeOfAccreditation> scopeOfAccreditation;

    @Column(name ="process_instance_id")
    private String processInstanceId;


}
