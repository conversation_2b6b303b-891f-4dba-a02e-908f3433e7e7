# Kafka Notification Implementation for Complaint Workflow

## Overview

The NotifyAgentDelegate has been updated to send notifications via Ka<PERSON>ka instead of just logging. This enables real-time notifications to agents and agent leads when complaints are assigned or escalated.

## Implementation Details

### Updated NotifyAgentDelegate

**Location**: `workflow-integration/src/main/java/com/workflowenginee/workflow/delegate/complaint/NotifyAgentDelegate.java`

**Key Features:**
- ✅ Sends Kafka notifications to specific agents
- ✅ Sends role-based notifications to agent leads for escalations
- ✅ Uses existing NotificationService infrastructure
- ✅ Handles both regular assignments and escalations
- ✅ Comprehensive error handling and logging

### Notification Types

#### 1. **Agent Assignment Notification**
When a complaint is assigned to a specific agent:

```java
notificationService.notifySpecificUser(
    agentId,                                    // userId (agent ID)
    referenceNumber,                            // referenceNumber
    complaintId,                                // applicationId
    "New Complaint Assignment",                 // subject
    "A new complaint has been assigned to you", // message
    "COMPLAINTS"                                // applicationType
);
```

**Kafka Message Structure:**
```json
{
  "id": "uuid",
  "name": "Agent Name",
  "subject": "New Complaint Assignment",
  "recipient": "agent-123",
  "message": "A new complaint REF-2025-001 has been assigned to you for review",
  "sentAt": "2025-06-16T06:00:00",
  "type": "IN_APP",
  "status": "PENDING",
  "applicationType": "COMPLAINTS",
  "applicationId": "cc610b03-df26-410b-8623-236e47afdc6c"
}
```

#### 2. **Escalation Notification**
When a complaint is escalated to agent leads:

```java
NotifyUsersByRoleDto roleNotificationDto = NotifyUsersByRoleDto.builder()
    .role("AGENT_LEAD")
    .referenceNumber(referenceNumber)
    .applicationId(complaintId)
    .applicationStatus("ESCALATED")
    .applicationType("COMPLAINTS")
    .build();

notificationService.notifyUsersByRole(roleNotificationDto);
```

**Kafka Message Structure:**
```json
{
  "id": "uuid",
  "name": "Agent Lead Name",
  "subject": "Application Submitted",
  "recipient": "agent-lead-123",
  "message": "A new application REF-2025-001 has been created and is ready for assignment",
  "sentAt": "2025-06-16T06:00:00",
  "type": "IN_APP",
  "status": "PENDING",
  "applicationType": "COMPLAINTS",
  "applicationId": "cc610b03-df26-410b-8623-236e47afdc6c"
}
```

## Kafka Configuration

### Topic Configuration
- **Default Topic**: Configured via `${spring.kafka.template.default-topic}`
- **Bootstrap Servers**: Configured via `${spring.kafka.bootstrap-servers}`

### Producer Configuration
- **Key Serializer**: StringSerializer
- **Value Serializer**: JsonSerializer
- **Retries**: 3
- **Retry Backoff**: 1000ms
- **Max Block**: 10000ms
- **Acks**: "1"

## Workflow Integration

### Process Variables Set
- `agentNotified`: boolean - Indicates if agent notification was sent
- `notifiedAgent`: string - ID of the notified agent
- `agentLeadsNotified`: boolean - Indicates if agent leads were notified (escalation only)
- `notificationError`: string - Error message if notification fails

### Activity Detection
The delegate automatically detects if it's being called for escalation:

```java
String currentActivity = execution.getCurrentActivityId();
if ("notifyEscalation".equals(currentActivity)) {
    // Send escalation notifications to agent leads
}
```

## Testing the Kafka Notifications

### 1. Start the Workflow
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/start/cc610b03-df26-410b-8623-236e47afdc6c
```

### 2. Send NotifyAgent Signal
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/signal/27567c21-4a7a-11f0-b985-14857f938c25
Content-Type: application/json

{
  "signalName": "NotifyAgent"
}
```

**Expected Kafka Messages:**
1. **Agent Assignment Notification** - Sent to the assigned agent
2. **Process continues** to agent decision gateway

### 3. If Escalation Occurs
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
Content-Type: application/json

{
  "signalName": "Escalated"
}
```

**Expected Kafka Messages:**
1. **Escalation Notification** - Sent to all users with "AGENT_LEAD" role

## Monitoring Kafka Messages

### Check Kafka Logs
Look for these log messages in the workflow integration service:

```
INFO  - [Process: 27567c21-4a7a-11f0-b985-14857f938c25] Sending Kafka notification to agent: agent-123 for complaint: REF-2025-001
INFO  - [Process: 27567c21-4a7a-11f0-b985-14857f938c25] Kafka notification sent successfully to agent: agent-123
INFO  - [Process: 27567c21-4a7a-11f0-b985-14857f938c25] Sending escalation notification to agent leads
```

### Kafka Consumer Logs
Check the notification service logs for successful message delivery:

```
INFO  - Notification sent to user [Agent Name] - topic: notifications, offset: 123, partition: 0
INFO  - Successfully sent notifications to 3 users with role AGENT_LEAD
```

## Error Handling

### Service Unavailability
- If CompanyClient is unavailable, notifications are skipped gracefully
- Process variables indicate notification status
- Workflow continues regardless of notification success/failure

### Kafka Unavailability
- Notifications are attempted with retry mechanism
- Failed notifications are logged but don't stop the workflow
- Process variables track notification errors

## Benefits

1. **Real-time Notifications**: Agents receive immediate notifications via Kafka
2. **Scalable**: Supports multiple notification consumers
3. **Resilient**: Workflow continues even if notifications fail
4. **Auditable**: All notification attempts are logged
5. **Flexible**: Supports both individual and role-based notifications
6. **Integrated**: Uses existing notification infrastructure

## Configuration Requirements

### Application Properties
```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    template:
      default-topic: ${KAFKA_NOTIFICATION_TOPIC:notifications}
```

### Required Services
1. **Kafka Broker** - Running on configured bootstrap servers
2. **Company Service** - For fetching user details and role-based users
3. **Notification Consumer** - To process the Kafka messages

The implementation provides a robust, scalable notification system that integrates seamlessly with the existing complaint workflow infrastructure.
