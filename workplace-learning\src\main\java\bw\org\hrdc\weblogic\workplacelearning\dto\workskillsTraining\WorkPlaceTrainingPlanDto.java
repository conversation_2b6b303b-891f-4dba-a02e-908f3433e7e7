package bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * DTO for managing Work Place Training Plan details.
 */
@Data
@Schema(
        name = "WorkPlaceTrainingPlan",
        description = "Schema to hold Work Place Training Plan details"
)
public class WorkPlaceTrainingPlanDto {

    @Schema(description = "Unique identifier for the training plan", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @Schema(description = "Unique identifier for the user who submitted the training plan", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID userId;

    @NotNull(message = "Organization ID cannot be null")
    @Schema(description = "Unique identifier for the organization submitting the training plan", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID organisationId;

    @Schema(description = "Application status of the training plan", example = "Pending")
    private String applicationStatus;
    
    @Schema(description = "Application state of the training plan", example = "IN_PROCESSING")
    private String applicationState;
    
    @Schema(description = "Application number", example = "WTP-2023-001")
    private String applicationNumber;
    
    @Schema(description = "Reference number", example = "REF-2023-001")
    private String referenceNumber;
    
    @Schema(description = "Financial year", example = "2023-2024")
    private String financialYear;
    
    @Schema(description = "Location", example = "Gaborone")
    private String location;
    
    @Schema(description = "Contact date", example = "2023-05-15")
    private LocalDate contactDate;
    
    @Schema(description = "Submission date", example = "2023-05-20")
    private LocalDate submissionDate;
    
    @Schema(description = "Assigned agent", example = "agent1")
    private String assignedAgent;
    
    @Schema(description = "Assigned agent lead", example = "agentlead1")
    private String assignedAgentLead;
    
    @Schema(description = "Assigned officer lead", example = "officerlead1")
    private String assignedOfficerLead;
    
    @Schema(description = "Assigned officer", example = "officer1")
    private String assignedOfficer;
    
    @Schema(description = "Assigned manager", example = "manager1")
    private String assignedManager;
    
    @Schema(description = "Deleted status", example = "false")
    private Boolean deleted;
    
    @Schema(description = "Date when the record was deleted", example = "2023-06-01T10:30:00")
    private Date deletedDate;
    
    @Schema(description = "List of course details")
    private List<CourseDetailsDto> courseDetails;
    
    @Schema(description = "Company details from account service (includes employees, contact person, and registration details)")
    private Object company;
    
    @Schema(description = "Date when the record was created", example = "2023-05-01T09:00:00")
    private LocalDateTime createdDate;
    
    @Schema(description = "Date when the record was last modified", example = "2023-05-10T14:30:00")
    private LocalDateTime lastModifiedDate;
    
    @Schema(description = "User who created the record", example = "john.doe")
    private String createdBy;
    
    @Schema(description = "User who last modified the record", example = "jane.smith")
    private String lastModifiedBy;
}
