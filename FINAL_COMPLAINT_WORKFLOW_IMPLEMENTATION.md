# Final Complaint Lifecycle Workflow Implementation

## Overview

This implementation provides a complete BPMN-based complaint lifecycle workflow with intermediate signal control, integrated between the workflow-integration service and the workplace-learning service. The workflow features signal-based flow control for better process management.

## Architecture

### Services Involved
1. **Workflow Integration Service** (Port 8090) - Orchestrates the complaint lifecycle
2. **Workplace Learning Service** (Port 8091) - Manages complaint data and business logic

## Workflow Structure

### Main Flow Sequence
1. **Start** → Complaint Raised
2. **Fetch Complaint Details** → Initial data retrieval
3. **Notify Agent** → Alert agents about the complaint
4. **Intermediate Signal** → Wait for agent response signal (`NotifyAgent`)
5. **Fetch After Signal** → Retrieve updated data and determine agent decision
6. **Agent Decision Gateway** → Choose path (Close/Chat/Escalate)

### Decision Paths

#### Path 1: Agent Close
- Agent Decision → Agent Closes Complaint → End (Closed by Agent)

#### Path 2: Agent Chat
- Agent Decision → Chat With Client → End (Closed after Chat)

#### Path 3: Escalation
- Agent Decision → Notify Escalation → Intermediate Signal (`Escalated`) → Fetch After Escalation → Escalated Decision Gateway
  - **Sub-path 3a**: Agent Lead Close → End (Closed by Agent Lead)
  - **Sub-path 3b**: Chat With Client → End (Closed after Chat) *[Shared with Path 2]*

## Key Components

### 1. BPMN Workflow Definition
- **File**: `workflow-integration/src/main/resources/processes/complaint-lifecycle.bpmn20.xml`
- **Process ID**: `complaintLifecycleProcess`
- **Signals**: `notifyAgentSignal`, `escalatedSignal`

### 2. Workflow Delegates
Located in `workflow-integration/src/main/java/com/workflowenginee/workflow/delegate/complaint/`:

1. **FetchComplaintDelegate** - Initial complaint data retrieval
2. **NotifyAgentDelegate** - Agent notifications (used for both initial and escalation notifications)
3. **FetchAfterSignalDelegate** - Post-signal data fetch with automatic agent decision logic
4. **CloseComplaintDelegate** - Agent complaint closure
5. **ChatWithClientDelegate** - Client communication (shared between regular and escalated paths)
6. **FetchAfterEscalationDelegate** - Escalation processing with automatic escalation decision logic
7. **CloseComplaintByAgentLeadDelegate** - Agent lead complaint closure

### 3. API Integration
- **WorkplaceLearningClient** - Feign client with complaint endpoints
- **WorkplaceLearningClientFallback** - Fallback implementation
- **ComplaintController** - Extended with workflow-specific endpoints
- **ComplaintService** - Enhanced with workflow integration methods

### 4. Controller Endpoints

#### Workflow Integration Service Endpoints:
```
POST /api/v1/workflow/complaint-lifecycle/start/{complaintId}
POST /api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
GET  /api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

#### Workplace Learning Service Endpoints:
```
PUT  /api/v1/complaints/{id}/status
POST /api/v1/complaints/{id}/comments
PUT  /api/v1/complaints/{id}/assign
PUT  /api/v1/complaints/{id}/close
PUT  /api/v1/complaints/{id}/escalate
```

## Usage Examples

### 1. Start Complaint Workflow
```bash
POST /api/v1/workflow/complaint-lifecycle/start/{complaintId}
```

### 2. Send Agent Response Signal
```bash
POST /api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
Content-Type: application/json

{
  "signalName": "NotifyAgent"
}
```

### 3. Send Escalation Signal
```bash
POST /api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
Content-Type: application/json

{
  "signalName": "Escalated"
}
```

### 4. Get Workflow Status
```bash
GET /api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

## Key Features

### 1. Intermediate Signal Control
- Workflow pauses at signal events for external control
- Two signal points: after agent notification and after escalation notification

### 2. Automatic Decision Logic
- **FetchAfterSignalDelegate** automatically determines agent decisions based on complaint data
- **FetchAfterEscalationDelegate** automatically determines escalation decisions
- Decisions based on priority, complaint type, department, etc.

### 3. Shared Components
- Single **ChatWithClientDelegate** used for both regular and escalated communication
- Single **NotifyAgentDelegate** used for both initial and escalation notifications

### 4. Error Handling
- Comprehensive fallback mechanisms
- Service unavailability handling
- Default decisions when data is unavailable

### 5. Audit Trail
- All actions logged in complaint system
- Process variables track workflow state
- Comprehensive error logging

## Decision Logic

### Agent Decision Logic (FetchAfterSignalDelegate)
- **High/Urgent Priority** → Escalate
- **Policy Violations** → Escalate
- **Management Department** → Escalate
- **Simple Inquiries** → Chat
- **Technical Issues** → Close
- **Complex Complaints** → Escalate
- **Default** → Chat

### Escalation Decision Logic (FetchAfterEscalationDelegate)
- **High/Urgent Priority** → Close (Agent Lead handles directly)
- **Policy Violations** → Close
- **Management Department** → Close
- **Complex/Service Quality Issues** → Chat
- **Default** → Chat

## Testing

1. Create a complaint in workplace learning system
2. Start workflow: `POST /complaint-lifecycle/start/{complaintId}`
3. Send agent signal: `POST /complaint-lifecycle/signal/{processInstanceId}` with `"signalName": "NotifyAgent"`
4. Monitor workflow: `GET /complaint-lifecycle/status/{processInstanceId}`
5. For escalation: Send escalation signal with `"signalName": "Escalated"`

## Benefits

- **Signal-based Control**: External control over workflow progression
- **Simplified Structure**: Single chat and close components
- **Automatic Decisions**: Intelligent decision-making based on complaint data
- **Robust Error Handling**: Graceful degradation when services are unavailable
- **Comprehensive Logging**: Full audit trail and monitoring capabilities
