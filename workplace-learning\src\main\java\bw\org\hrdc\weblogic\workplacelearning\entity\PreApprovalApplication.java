package bw.org.hrdc.weblogic.workplacelearning.entity;

import bw.org.hrdc.weblogic.workplacelearning.config.JsonStringListDeserializer;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Entity class for PreApprovalApplication.
 */
@Entity
@Table(name = "pre_approval_application")
@Data
public class PreApprovalApplication {

    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "uuid2")
    @Column(name = "id", columnDefinition = "uuid")
    private UUID id;

    @Column(name = "user_id", columnDefinition = "uuid")
    private UUID userId;

    @Column(name = "organisation_id", columnDefinition = "uuid", nullable = false)
    private UUID organisationId;

    @Column(name = "status")
    private String status;

    @Column(name = "state")
    private String state;

    @Column(name = "application_number", unique = true)
    private String applicationNumber;

    @Column(name = "reference_number", unique = true)
    private String referenceNumber;

    @Column(name = "reason_for_training", columnDefinition = "TEXT")
    private String reasonForTraining;

    @Column(name = "course_title")
    private String courseTitle;

    @Column(name = "training_provider")
    private String trainingProvider;

    @Column(name = "city_of_training")
    private String cityOfTraining;

    @Temporal(TemporalType.DATE)
    @Column(name = "training_start_date")
    private Date trainingStartDate;

    @Temporal(TemporalType.DATE)
    @Column(name = "training_end_date")
    private Date trainingEndDate;

    @OneToMany(mappedBy = "preApprovalApplication", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ParticularOfTraining> particularOfTrainings;

    @OneToMany(mappedBy = "preApprovalApplication", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ModuleDetails> moduleDetails;

    @OneToMany(mappedBy = "preApprovalApplication", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<EstimatedTrainingCosts> estimatedTrainingCosts;

    @Column(name = "subtotal", precision = 10, scale = 2)
    private BigDecimal subtotal;

    @Column(name = "total", precision = 10, scale = 2)
    private BigDecimal total;

    @Column(name = "vat_number", unique = true)
    private String vatNumber;

    @Column(name = "accreditation_evidence", columnDefinition = "TEXT")
    private String accreditationEvidence;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "employee_ids", columnDefinition = "json")
    @JsonDeserialize(using = JsonStringListDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private List<String> employeeIds;

    // Role-based workflow fields
    @Column(name = "assigned_agent")
    private String assignedAgent;

    @Column(name = "assigned_agent_lead")
    private String assignedAgentLead;

    @Column(name = "assigned_officer_lead")
    private String assignedOfficerLead;

    @Column(name = "assigned_officer")
    private String assignedOfficer;

    @Column(name = "assigned_manager")
    private String assignedManager;

    // Soft delete fields
    @Column(name = "deleted")
    private Boolean deleted = false;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "deleted_date")
    private Date deletedDate;

    // Audit fields
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_date", nullable = false)
    private Date createdDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_modified_date")
    private Date lastModifiedDate;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "total_learning_hours")
    private String totalLearningHours;

    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        lastModifiedDate = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        lastModifiedDate = new Date();
    }

    @Column(name = "process_instance_id", nullable = true)
    private String processInstanceId;
}
