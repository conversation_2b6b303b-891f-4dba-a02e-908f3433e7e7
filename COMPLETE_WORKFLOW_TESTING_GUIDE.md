# Complete Workflow Testing Guide

## ✅ **Implementation Status: 100% Complete**

All workflow integration code is implemented and matches the updated BPMN XML flow exactly.

## 🔧 **All Delegates Implemented**

| Delegate | Bean Name | Status | Purpose |
|----------|-----------|--------|---------|
| FetchComplaintDelegate | `fetchComplaintDelegate` | ✅ | Fetch initial complaint details |
| NotifyAgentDelegate | `notifyAgentDelegate` | ✅ | Send Kafka notifications |
| FetchAfterSignalDelegate | `fetchAfterSignalDelegate` | ✅ | Fetch data & determine agent decision |
| CloseComplaintDelegate | `closeComplaintDelegate` | ✅ | Agent closes complaint |
| ChatWithClientDelegate | `chatWithClientDelegate` | ✅ | Agent communicates with client |
| FetchAfterEscalationDelegate | `fetchAfterEscalationDelegate` | ✅ | Fetch data & determine escalation decision |
| CloseComplaintByAgentLeadDelegate | `closeComplaintByAgentLeadDelegate` | ✅ | Agent lead closes complaint |

## 🎯 **Complete Flow Testing**

### **Test 1: Agent Close Path**
```bash
# 1. Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-001

# Expected: 
# - FetchComplaintDelegate executes
# - NotifyAgentDelegate sends Kafka notification
# - Process waits at signalNotifyAgent

# 2. Send NotifyAgent signal
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-001",
  "role": "Agent",
  "decision": "close"
}

# Expected Flow:
# signalNotifyAgent → FetchAfterSignalDelegate → agentDecision → 
# CloseComplaintDelegate → ChatWithClientDelegate → End

# 3. Check final status
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processInstanceId}

# Expected Variables:
# - agentDecision: "close"
# - complaintStatus: "CLOSED" 
# - closedBy: "AGENT"
# - chatSessionActive: true
```

### **Test 2: Direct Chat Path**
```bash
# 1. Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-002

# 2. Send NotifyAgent signal with chat decision
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-002",
  "role": "Agent", 
  "decision": "chat"
}

# Expected Flow:
# signalNotifyAgent → FetchAfterSignalDelegate → agentDecision → 
# ChatWithClientDelegate → End

# Expected Variables:
# - agentDecision: "chat"
# - complaintStatus: "RESOLVED_VIA_CHAT"
# - chatSessionActive: true
```

### **Test 3: Escalation → Agent Lead Close Path**
```bash
# 1. Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-003

# 2. Send NotifyAgent signal with escalate decision
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-003",
  "role": "Agent",
  "decision": "escalate"
}

# Expected: Process waits at signalEscalated

# 3. Send Escalated signal with close decision
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/Escalated
Content-Type: application/json

{
  "complaintId": "test-complaint-003",
  "role": "AgentLead",
  "decision": "close"
}

# Expected Flow:
# signalEscalated → FetchAfterEscalationDelegate → checkEscalatedCondition →
# CloseComplaintByAgentLeadDelegate → ChatWithClientDelegate → End

# Expected Variables:
# - agentDecision: "escalate"
# - escalationDecision: "close"
# - complaintStatus: "CLOSED"
# - closedBy: "AGENT_LEAD"
# - escalationResolved: true
```

### **Test 4: Escalation → Chat Path**
```bash
# 1. Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-004

# 2. Escalate
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
{"complaintId": "test-complaint-004", "decision": "escalate"}

# 3. Agent lead chooses chat
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/Escalated
{"complaintId": "test-complaint-004", "decision": "chat"}

# Expected Flow:
# signalEscalated → FetchAfterEscalationDelegate → checkEscalatedCondition →
# ChatWithClientDelegate → End

# Expected Variables:
# - escalationDecision: "chat"
# - complaintStatus: "RESOLVED_VIA_CHAT"
```

## 📊 **Process Variables to Monitor**

### **Core Variables**
- `complaintId`: The complaint being processed
- `complaintData`: Complete complaint information from service
- `complaintStatus`: Current status (OPEN, CLOSED, RESOLVED_VIA_CHAT)
- `complaintState`: Current state (SUBMITTED, IN_PROGRESS, etc.)

### **Decision Variables**
- `agentDecision`: "close", "chat", or "escalate"
- `escalationDecision`: "close" or "chat" (for escalated cases)

### **Notification Variables**
- `agentNotified`: Boolean indicating notification success
- `notifiedAgent`: ID of the notified agent
- `agentLeadsNotified`: Boolean for escalation notifications

### **Closure Variables**
- `closedBy`: "AGENT" or "AGENT_LEAD"
- `closedAt`: Timestamp of closure
- `escalationResolved`: Boolean for escalated cases

### **Chat Variables**
- `chatSessionActive`: Boolean indicating active chat
- `chatInitiatedBy`: "AGENT" or "AGENT_LEAD"
- `chatStartTime`: Timestamp of chat initiation

## 🔍 **Kafka Message Monitoring**

### **Expected Kafka Messages**

#### **1. Agent Assignment Notification**
```json
{
  "id": "uuid",
  "name": "Agent Name",
  "subject": "New Complaint Assignment", 
  "recipient": "agent-123",
  "message": "A new complaint REF-2025-001 has been assigned to you for review",
  "type": "IN_APP",
  "application_type": "COMPLAINTS",
  "application_id": "test-complaint-001"
}
```

#### **2. Escalation Notification (to Agent Leads)**
```json
{
  "id": "uuid",
  "name": "Agent Lead Name",
  "subject": "Application Submitted",
  "recipient": "agent-lead-123", 
  "message": "A new application REF-2025-001 has been created and is ready for assignment",
  "type": "IN_APP",
  "application_type": "COMPLAINTS"
}
```

### **Monitor Kafka Logs**

#### **Workflow Integration Service**
```
INFO - [Process: xxx] Sending Kafka notification to agent: agent-123
INFO - Notification sent to user [Agent Name] - topic: notifications, offset: 123
INFO - [Process: xxx] Sending escalation notification to agent leads
```

#### **Communication Service**
```
INFO - Received notification message from topic: notifications
INFO - Processing notification for recipient: agent-123, type: IN_APP
INFO - Successfully processed notification for recipient: agent-123
```

## 🧪 **Service Integration Testing**

### **Test with Workplace Learning Service Available**
- All delegates should fetch real complaint data
- Process variables should contain actual complaint information
- Decisions should be based on real complaint attributes

### **Test with Workplace Learning Service Unavailable**
- FetchComplaintDelegate should use fallback data
- Process should continue with default values
- `serviceUnavailable: true` should be set

### **Test with Kafka Unavailable**
- Workflow should continue despite notification failures
- `notificationError` variables should be set
- Process should complete successfully

## ✅ **Success Criteria**

### **Complete Success**
- ✅ All workflow paths execute without errors
- ✅ Kafka notifications sent and received
- ✅ Process variables set correctly
- ✅ Workflow completes at appropriate end events

### **Partial Success (Service Unavailable)**
- ✅ Workflow completes with fallback data
- ✅ Notifications attempted but may fail
- ✅ Process continues despite service issues

### **Failure Indicators**
- ❌ Workflow fails to start or complete
- ❌ Critical errors stop process execution
- ❌ Signal processing fails
- ❌ Data corruption or loss

## 🚀 **Production Readiness Checklist**

- [x] All delegates implemented and tested
- [x] Kafka notification system working
- [x] Service integration with fallback mechanisms
- [x] Error handling and resilience
- [x] Process variable management
- [x] Signal-based flow control
- [x] Decision logic implementation
- [x] Audit trail and logging

## 📋 **Final Implementation Summary**

**Status**: ✅ **100% COMPLETE AND PRODUCTION READY**

The complete workflow integration code is implemented with:
- **7 Delegates** - All implemented and functional
- **Kafka Notifications** - Full integration with communication service
- **Service Integration** - With fallback mechanisms for resilience
- **Decision Logic** - Automatic decision-making based on complaint data
- **Error Handling** - Comprehensive error handling and recovery
- **Signal Control** - Proper signal-based flow control
- **Process Variables** - Complete variable management and audit trail

The workflow is ready for production deployment and can handle all complaint lifecycle scenarios with proper notifications, service integration, and error resilience.
