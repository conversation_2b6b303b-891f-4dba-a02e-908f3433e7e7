package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface WorkPlaceTrainingPlanCommentsRepository extends JpaRepository<WorkPlaceTrainingPlanComments, UUID> {

    @Query("SELECT c FROM WorkPlaceTrainingPlanComments c WHERE c.trainingPlan.id = :trainingPlanId ORDER BY c.timestamp DESC")
    List<WorkPlaceTrainingPlanComments> findByTrainingPlanIdOrderByTimestampDesc(@Param("trainingPlanId") UUID trainingPlanId);
}