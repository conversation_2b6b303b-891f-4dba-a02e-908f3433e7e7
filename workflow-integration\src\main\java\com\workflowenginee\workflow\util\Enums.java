package com.workflowenginee.workflow.util;

public class Enums {

     public enum State {
        DRAFT, //Applicant did not submit
        SUBMITTED, //Applicant submitted, not yet assigned for assessment
        IN_PROCESSING, //Assigned to Agent for vetting
        IN_REVIEW, // Assigned to officer, under vetting
        IN_APPROVAL, //Assigned to manager for approval
    }

    public enum Status {
        INITIAL, //Not yet submitted, still in draft state
        PENDING, //Not in draft and has been submitted but not yet acted upon
        APPROVED, //Accepted by the manager and the application is closed
        REJECTED, //Did not meet requirements
        CHANGE_REQUEST, //Changes requested
        PRE_APPROVED, //Approved by the officer/officer lead under review state
        EXPIRED, //Still in draft, Time allowed passed before it was submitted
        CANCELLED, //Still under draft/ submitted but was not assigned Closed while still awaiting processing
    }

    public enum ApplicationType {
        RECOGNITION,
        NOC,
        PRE_APPROVAL,
        WORK_SKILLS,
        APPEALS,
        COMPLAINTS
    }

    public enum Role{
        AGENT_LEAD,
        AGENT,
        OFFICER_LEAD,
        OFFICER,
        MANAGER,
    }

    public enum NotificationType {
    SMS,
    EMAIL,
    IN_APP
}

  public enum ComplaintStatus {
        OPEN, AWAITING_CLIENT_FEEDBACK, IN_PROGRESS, CLOSED
    }

    public enum ComplaintState {
        SUBMITTED, UNDER_REVIEW, ESCALATED, COMPLETED
    }

}
