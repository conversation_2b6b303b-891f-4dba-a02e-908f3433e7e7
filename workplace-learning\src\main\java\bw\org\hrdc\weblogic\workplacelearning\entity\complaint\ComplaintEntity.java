package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Auditable;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import io.swagger.v3.oas.annotations.media.Schema;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 20:50
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 20:50
 */
@Entity
@Table(name = "complaints")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintEntity extends Auditable implements Serializable {

    private String organisationId;

    @Enumerated(EnumType.STRING)
    private Enums.Department department;

    private String typeOfComplaint;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String reference;

    @Column(unique = true)
    private String referenceNumber;

    @Enumerated(EnumType.STRING)
    private Enums.ComplaintStatus status;

    @Enumerated(EnumType.STRING)
    private Enums.ComplaintState state;

    private String assignee;

    @Enumerated(EnumType.STRING)
    private Enums.CategoryComplaint category;

    @OneToMany(mappedBy = "complaint", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private List<ComplaintDocument> documents = new ArrayList<>();

    @OneToMany(mappedBy = "complaint", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private List<Comment> comments = new ArrayList<>();

    @OneToMany(mappedBy = "complaint", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private List<AuditLog> auditLogs = new ArrayList<>();

    @Schema(description = "Process instance ID for the application", example = "pi-1234567890")
    private String processInstanceId;
}
