package com.workflowenginee.workflow.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.flowable.engine.RuntimeService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Workflow Controller for Complaint Lifecycle Management
 * Handles BPMN workflow operations with automatic decision making
 */
@RestController
@RequestMapping("/api/v1/workflow")
public class WorkflowController {

    private static final Logger log = LoggerFactory.getLogger(WorkflowController.class);

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    
@GetMapping("/start-process/{applicationType}/{applicationNumber}")
    public ResponseEntity<?> getNCBSCApplication(@PathVariable String applicationType, @PathVariable String applicationNumber) {
        try {
            Map<String, Object> variables = Map.of(
                "applicationNumber", applicationNumber,
                "applicationType", applicationType,
                 "role", "Agent_lead"
            );
            System.out.println("[WorkflowController] Starting process for applicationType: " + applicationType + ", applicationNumber: " + applicationNumber);

            ProcessInstance processInstance = null;

                processInstance = runtimeService.startProcessInstanceByKey("Hrdc_workflow", variables);

            Map<String, Object> applicationData = null;
            int retries = 20;
            int delay = 500; 

            // Poll for application data with timeout
            for (int i = 0; i < retries; i++) {
                applicationData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "ApplicationData");
                if (applicationData != null) {
                    break;
                }
                Thread.sleep(delay);
            }

            if (applicationData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Process started and application data retrieved successfully",
                    "processInstanceId", processInstance.getId(),
                    "applicationData", applicationData
                ));
            }  else {
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for application data"
                ));
            }
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error retrieving NCBSC application: " + e.getMessage()
            ));
        }
    }
   
    @PostMapping("/resume-process/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeProcess(@PathVariable String processInstanceId, @PathVariable String signalType, @RequestBody Map<String, Object> WorkFlowPayload) {
        String activityId;
        String signalName;

        System.out.println("WorkFlowPayload: " + WorkFlowPayload);
        System.out.println("ProcessInstanceId: " + processInstanceId);
        System.out.println("SignalType: " + signalType);
        
        // Map signalType to the appropriate activity ID and signal name
        switch (signalType) {
            case "Agent_action":
                activityId = "AgentAction";
                signalName = "ProcessSignal_AgentAction";
                break;
            case "Officer_action":
                activityId = "OfficerAction";
                signalName = "ProcessSignal_OfficerAction";
                break;
            case "Manager_action":
                activityId = "ManagerAction";
                signalName = "ProcessSignal_ManagerAction";
                break;
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of(
                        "success", false,
                        "message", "Invalid signal type: " + signalType + ". Valid types are Agent_action, Officer_action, or Manager_action."
                    )
                );
        }

        // Find the execution that is waiting at the specified signal catch event
        Execution execution = runtimeService.createExecutionQuery()
            .processInstanceId(processInstanceId)
            .activityId(activityId)
            .singleResult();
        
        if (execution == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                Map.of(
                    "success", false,
                    "message", "No execution found waiting at " + activityId + " for process: " + processInstanceId
                )
            );
        }
        
        System.out.println("[Process: " + processInstanceId + "] Resuming with signal " + signalName + " at " + activityId);
        String executionId = execution.getId();

        Map<String, Object> variables = new HashMap<>();
        
        String applicationType = (String) WorkFlowPayload.get("applicationType");
        String referenceNumber = (String) WorkFlowPayload.get("referenceNumber");
        String role = (String) WorkFlowPayload.get("role");
        
        if (applicationType != null) {
            variables.put("applicationType", applicationType);
        }
        
        if (referenceNumber != null) {
            variables.put("referenceNumber", referenceNumber);
        }

        if(role != null) {
            variables.put("role", role);
        }
        
        runtimeService.signalEventReceived(signalName, executionId, variables);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Process " + processInstanceId + " resumed with signal type " + signalType 
        ));
    }

    @PostMapping("/startprocess-compaints/{complaintId}")
    public ResponseEntity<?> getComplaintsWorkflow(@PathVariable String complaintId){

        try {
            Map<String, Object> variables = Map.of(
                "complaintId", complaintId,
                 "role", "Agent_lead"
            );
            System.out.println("[WorkflowComplaintsController] Starting process for applicationType: " + complaintId );

            ProcessInstance processInstance = null;

            processInstance = runtimeService.startProcessInstanceByKey("workflow_complaints", variables);

            Map<String, Object> applicationData = null;
            int retries = 20;
            int delay = 500; 

            // Poll for application data with timeout
            for (int i = 0; i < retries; i++) {
                applicationData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "applicationData");
                if (applicationData != null) {
                    break;
                }
                Thread.sleep(delay);
            }


            if (applicationData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Process started and application data retrieved successfully",
                    "processInstanceId", processInstance.getId(),
                    "Data", applicationData
                ));
            }  else {
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for application data"
                ));
            }
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error retrieving NCBSC application: " + e.getMessage()
            ));
        }
    }

    /**
     * Start Complaint Lifecycle Process
     * Initiates the BPMN complaint lifecycle workflow
     *
     * BPMN Flow: Start → Fetch Complaint Details → Notify Agent → Intermediate Signal
     *
     * @param complaintId The complaint identifier
     * @return ResponseEntity with process instance details
     */
    @PostMapping("/start-complaint-lifecycle/{complaintId}")
    public ResponseEntity<?> startComplaintLifecycle(@PathVariable String complaintId) {
        try {
            log.info("Starting complaint lifecycle process for complaint: {}", complaintId);

            // Initialize process variables
            Map<String, Object> variables = Map.of(
                "complaintId", complaintId,
                "role", "Agent"
            );

            // Start the BPMN process - matches process definition key in BPMN XML
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("complaintLifecycleProcess", variables);

            log.info("Complaint lifecycle process started with ID: {} for complaint: {}",
                processInstance.getId(), complaintId);

            // Poll for complaint data from FetchComplaintDelegate
            Map<String, Object> complaintData = null;
            int retries = 20;
            int delay = 500; // 500ms delay between retries

            for (int i = 0; i < retries; i++) {
                complaintData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "complaintData");
                if (complaintData != null) {
                    log.info("Complaint data retrieved successfully for process: {}", processInstance.getId());
                    break;
                }
                Thread.sleep(delay);
            }

            if (complaintData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Complaint lifecycle process started successfully",
                    "processInstanceId", processInstance.getId(),
                    "complaintId", complaintId,
                    "complaintData", complaintData,
                    "status", "Process is waiting at signalNotifyAgent - ready for agent decision"
                ));
            } else {
                log.warn("Timeout waiting for complaint data for process: {}", processInstance.getId());
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for complaint data - process may still be running",
                    "processInstanceId", processInstance.getId(),
                    "complaintId", complaintId
                ));
            }

        } catch (Exception e) {
            log.error("Error starting complaint lifecycle process for complaint: {}", complaintId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting complaint lifecycle process: " + e.getMessage(),
                "complaintId", complaintId
            ));
        }
    }

    /**
     * Resume Complaint Lifecycle Process with Signal
     * Sends signals to continue the BPMN workflow with automatic decision making
     *
     * BPMN Signals:
     * - NotifyAgent: Triggers automatic agent decision (close/escalate)
     * - Escalated: Triggers automatic escalation completion
     *
     * @param processInstanceId The process instance ID
     * @param signalType The signal type (NotifyAgent or Escalated)
     * @param workflowPayload Request payload containing complaintId
     * @return ResponseEntity with operation result
     */
    @PostMapping("/resume-complaint-lifecycle/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeComplaintLifecycle(
            @PathVariable String processInstanceId,
            @PathVariable String signalType,
            @RequestBody Map<String, Object> workflowPayload) {

        log.info("Resuming complaint lifecycle process: {} with signal: {}", processInstanceId, signalType);
        log.debug("Workflow payload: {}", workflowPayload);

        try {
            // Validate process instance exists
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "Process instance not found: " + processInstanceId
                ));
            }

            // Extract and validate payload
            String complaintId = (String) workflowPayload.get("complaintId");
            String role = (String) workflowPayload.get("role");

            if (complaintId == null || complaintId.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of(
                    "success", false,
                    "message", "complaintId is required in request body"
                ));
            }

            // Prepare process variables
            Map<String, Object> variables = new HashMap<>();
            variables.put("complaintId", complaintId);
            if (role != null) {
                variables.put("role", role);
            }

            // Handle signal types based on BPMN XML signal definitions
            switch (signalType) {
                case "NotifyAgent":
                    // Signal: signalNotifyAgent in BPMN
                    // Triggers: FetchAfterSignalDelegate → Automatic Decision → Gateway
                    log.info("Sending NotifyAgent signal for process: {} - automatic decision will be made", processInstanceId);
                    runtimeService.signalEventReceived("NotifyAgent", processInstanceId, variables);

                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "message", "NotifyAgent signal sent - automatic decision will be made based on complaint data",
                        "processInstanceId", processInstanceId,
                        "signalType", signalType,
                        "nextStep", "System will automatically decide to close or escalate based on complaint attributes"
                    ));

                case "Escalated":
                    // Signal: signalEscalated in BPMN
                    // Triggers: FetchAfterEscalationDelegate → Agent Lead Close → Client Notification
                    log.info("Sending Escalated signal for process: {} - escalation will be completed automatically", processInstanceId);
                    runtimeService.signalEventReceived("Escalated", processInstanceId, variables);

                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "message", "Escalated signal sent - escalation will be completed automatically",
                        "processInstanceId", processInstanceId,
                        "signalType", signalType,
                        "nextStep", "Agent lead will close the complaint and client will be notified"
                    ));

                default:
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of(
                        "success", false,
                        "message", "Invalid signal type: " + signalType + ". Valid types are 'NotifyAgent' or 'Escalated'.",
                        "validSignalTypes", List.of("NotifyAgent", "Escalated")
                    ));
            }

        } catch (Exception e) {
            log.error("Error resuming complaint lifecycle process: {} with signal: {}", processInstanceId, signalType, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error resuming complaint lifecycle process: " + e.getMessage(),
                "processInstanceId", processInstanceId,
                "signalType", signalType
            ));
        }
    }

    @PostMapping("/resume-complaint-process/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeComplaintProcess(@PathVariable String processInstanceId, @PathVariable String signalType, @RequestBody Map<String, Object> WorkFlowPayload) {
        String activityId;
        String signalName;

        System.out.println("WorkFlowPayload: " + WorkFlowPayload);
        System.out.println("ProcessInstanceId: " + processInstanceId);
        System.out.println("SignalType: " + signalType);
        
        // Map signalType to the appropriate activity ID and signal name
        switch (signalType) {
            case "Agent_action":
                activityId = "AgentAction";
                signalName = "ProcessSignal_AgentAction";
                break;
            case "Officer_action":
                activityId = "OfficerAction";
                signalName = "ProcessSignal_OfficerAction";
                break;
            case "Manager_action":
                activityId = "ManagerAction";
                signalName = "ProcessSignal_ManagerAction";
                break;
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of(
                        "success", false,
                        "message", "Invalid signal type: " + signalType + ". Valid types are Agent_action, Officer_action, or Manager_action."
                    )
                );
        }
        
        // Find the execution that is waiting at the specified signal catch event
        Execution execution = runtimeService.createExecutionQuery()
            .processInstanceId(processInstanceId)
            .activityId(activityId)
            .singleResult();
        
        if (execution == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                Map.of(
                    "success", false,
                    "message", "No execution found waiting at " + activityId + " for process: " + processInstanceId
                )
            );
        }
        
        System.out.println("[Process: " + processInstanceId + "] Resuming with signal " + signalName + " at " + activityId);
        String executionId = execution.getId();

        Map<String, Object> variables = new HashMap<>();
        
        String complaintId = (String) WorkFlowPayload.get("complaintId");
        String applicationType = (String) WorkFlowPayload.get("applicationType");
        String role = (String) WorkFlowPayload.get("role");
        
        if (complaintId != null) {
            variables.put("complaintId", complaintId);
        }

        if(role != null) {
            variables.put("role", role);
        }
        if (applicationType != null) {
            variables.put("applicationType", applicationType);
        }
        
        runtimeService.signalEventReceived(signalName, executionId, variables);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Complaint process " + processInstanceId + " resumed with signal type " + signalType
        ));
    }





    /**
     * Get Complaint Lifecycle Process Status
     * Retrieves current status, variables, and active activities of the BPMN process
     *
     * @param processInstanceId The process instance ID
     * @return ResponseEntity with detailed process status
     */
    @GetMapping("/complaint-lifecycle/status/{processInstanceId}")
    public ResponseEntity<?> getComplaintLifecycleStatus(@PathVariable String processInstanceId) {
        try {
            log.info("Getting complaint lifecycle status for process: {}", processInstanceId);

            // Get process instance
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                // Check if process has ended
                boolean processEnded = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .finished()
                    .count() > 0;

                if (processEnded) {
                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "message", "Process has completed",
                        "processInstanceId", processInstanceId,
                        "isEnded", true,
                        "status", "COMPLETED"
                    ));
                } else {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of(
                            "success", false,
                            "message", "Process instance not found",
                            "processInstanceId", processInstanceId
                        ));
                }
            }

            // Get process variables
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);

            // Get current activities
            List<String> activeActivities = runtimeService.getActiveActivityIds(processInstanceId);

            // Determine current status based on active activities
            String currentStatus = determineProcessStatus(activeActivities, variables);
            String nextAction = determineNextAction(activeActivities, variables);

            // Build response map
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Complaint lifecycle status retrieved successfully");
            response.put("processInstanceId", processInstanceId);
            response.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
            response.put("activeActivities", activeActivities);
            response.put("currentStatus", currentStatus);
            response.put("nextAction", nextAction);
            response.put("variables", variables);
            response.put("isEnded", processInstance.isEnded());
            response.put("complaintId", variables.get("complaintId"));
            response.put("agentDecision", variables.get("agentDecision"));
            response.put("escalationDecision", variables.get("escalationDecision"));
            response.put("closedBy", variables.get("closedBy"));
            response.put("clientNotified", variables.get("clientNotified"));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error getting complaint lifecycle status for process {}", processInstanceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "Failed to get status: " + e.getMessage(),
                    "processInstanceId", processInstanceId
                ));
        }
    }

    /**
     * Determines the current process status based on active activities
     */
    private String determineProcessStatus(List<String> activeActivities, Map<String, Object> variables) {
        if (activeActivities.isEmpty()) {
            return "COMPLETED";
        }

        if (activeActivities.contains("signalNotifyAgent")) {
            return "WAITING_FOR_AGENT_DECISION";
        }

        if (activeActivities.contains("signalEscalated")) {
            return "WAITING_FOR_ESCALATION_COMPLETION";
        }

        if (activeActivities.contains("fetchComplaintDetails")) {
            return "FETCHING_COMPLAINT_DATA";
        }

        if (activeActivities.contains("notifyAgent")) {
            return "NOTIFYING_AGENT";
        }

        if (activeActivities.contains("agentClose")) {
            return "AGENT_CLOSING_COMPLAINT";
        }

        if (activeActivities.contains("agentLeadClose")) {
            return "AGENT_LEAD_CLOSING_COMPLAINT";
        }

        if (activeActivities.contains("sendClientNotification") ||
            activeActivities.contains("sendClientNotificationAfterEscalation")) {
            return "SENDING_CLIENT_NOTIFICATION";
        }

        return "IN_PROGRESS";
    }

    /**
     * Determines the next required action based on current state
     */
    private String determineNextAction(List<String> activeActivities, Map<String, Object> variables) {
        if (activeActivities.isEmpty()) {
            return "Process completed - no action required";
        }

        if (activeActivities.contains("signalNotifyAgent")) {
            return "Send NotifyAgent signal to trigger automatic decision";
        }

        if (activeActivities.contains("signalEscalated")) {
            return "Send Escalated signal to complete escalation";
        }

        return "Process is executing automatically - no manual action required";
    }
}



