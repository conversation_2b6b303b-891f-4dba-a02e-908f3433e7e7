package com.workflowenginee.workflow.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.workflowenginee.workflow.util.Enums;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/workflow")
public class WorkflowController {

    @Autowired
    private RuntimeService runtimeService;

    
@GetMapping("/start-process/{applicationType}/{applicationNumber}")
    public ResponseEntity<?> getNCBSCApplication(@PathVariable String applicationType, @PathVariable String applicationNumber) {
        try {
            Map<String, Object> variables = Map.of(
                "applicationNumber", applicationNumber,
                "applicationType", applicationType,
                 "role", "Agent_lead"
            );
            System.out.println("[WorkflowController] Starting process for applicationType: " + applicationType + ", applicationNumber: " + applicationNumber);

            ProcessInstance processInstance = null;

                processInstance = runtimeService.startProcessInstanceByKey("Hrdc_workflow", variables);

            Map<String, Object> applicationData = null;
            int retries = 20;
            int delay = 500; 

            // Poll for application data with timeout
            for (int i = 0; i < retries; i++) {
                applicationData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "ApplicationData");
                if (applicationData != null) {
                    break;
                }
                Thread.sleep(delay);
            }

            if (applicationData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Process started and application data retrieved successfully",
                    "processInstanceId", processInstance.getId(),
                    "applicationData", applicationData
                ));
            }  else {
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for application data"
                ));
            }
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error retrieving NCBSC application: " + e.getMessage()
            ));
        }
    }
   
    @PostMapping("/resume-process/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeProcess(@PathVariable String processInstanceId, @PathVariable String signalType, @RequestBody Map<String, Object> WorkFlowPayload) {
        String activityId;
        String signalName;

        System.out.println("WorkFlowPayload: " + WorkFlowPayload);
        System.out.println("ProcessInstanceId: " + processInstanceId);
        System.out.println("SignalType: " + signalType);
        
        // Map signalType to the appropriate activity ID and signal name
        switch (signalType) {
            case "Agent_action":
                activityId = "AgentAction";
                signalName = "ProcessSignal_AgentAction";
                break;
            case "Officer_action":
                activityId = "OfficerAction";
                signalName = "ProcessSignal_OfficerAction";
                break;
            case "Manager_action":
                activityId = "ManagerAction";
                signalName = "ProcessSignal_ManagerAction";
                break;
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of(
                        "success", false,
                        "message", "Invalid signal type: " + signalType + ". Valid types are Agent_action, Officer_action, or Manager_action."
                    )
                );
        }

        // Find the execution that is waiting at the specified signal catch event
        Execution execution = runtimeService.createExecutionQuery()
            .processInstanceId(processInstanceId)
            .activityId(activityId)
            .singleResult();
        
        if (execution == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                Map.of(
                    "success", false,
                    "message", "No execution found waiting at " + activityId + " for process: " + processInstanceId
                )
            );
        }
        
        System.out.println("[Process: " + processInstanceId + "] Resuming with signal " + signalName + " at " + activityId);
        String executionId = execution.getId();

        Map<String, Object> variables = new HashMap<>();
        
        String applicationType = (String) WorkFlowPayload.get("applicationType");
        String referenceNumber = (String) WorkFlowPayload.get("referenceNumber");
        String role = (String) WorkFlowPayload.get("role");
        
        if (applicationType != null) {
            variables.put("applicationType", applicationType);
        }
        
        if (referenceNumber != null) {
            variables.put("referenceNumber", referenceNumber);
        }

        if(role != null) {
            variables.put("role", role);
        }
        
        runtimeService.signalEventReceived(signalName, executionId, variables);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Process " + processInstanceId + " resumed with signal type " + signalType 
        ));
    }

    @PostMapping("/startprocess-compaints/{complaintId}")
    public ResponseEntity<?> getComplaintsWorkflow(@PathVariable String complaintId){

        try {
            Map<String, Object> variables = Map.of(
                "complaintId", complaintId,
                 "role", "Agent_lead"
            );
            System.out.println("[WorkflowComplaintsController] Starting process for applicationType: " + complaintId );

            ProcessInstance processInstance = null;

            processInstance = runtimeService.startProcessInstanceByKey("workflow_complaints", variables);

            Map<String, Object> applicationData = null;
            int retries = 20;
            int delay = 500; 

            // Poll for application data with timeout
            for (int i = 0; i < retries; i++) {
                applicationData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "applicationData");
                if (applicationData != null) {
                    break;
                }
                Thread.sleep(delay);
            }


            if (applicationData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Process started and application data retrieved successfully",
                    "processInstanceId", processInstance.getId(),
                    "Data", applicationData
                ));
            }  else {
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for application data"
                ));
            }
                
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error retrieving NCBSC application: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/start-complaint-lifecycle/{complaintId}")
    public ResponseEntity<?> startComplaintLifecycle(@PathVariable String complaintId) {
        try {
            Map<String, Object> variables = Map.of(
                "complaintId", complaintId,
                "role", "Agent"
            );
            System.out.println("[WorkflowController] Starting complaint lifecycle process for complaint: " + complaintId);

            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("complaintLifecycleProcess", variables);

            Map<String, Object> complaintData = null;
            int retries = 20;
            int delay = 500;

            // Poll for complaint data with timeout
            for (int i = 0; i < retries; i++) {
                complaintData = (Map<String, Object>) runtimeService.getVariable(processInstance.getId(), "complaintData");
                if (complaintData != null) {
                    break;
                }
                Thread.sleep(delay);
            }

            if (complaintData != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Complaint lifecycle process started successfully",
                    "processInstanceId", processInstance.getId(),
                    "complaintData", complaintData
                ));
            } else {
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body(Map.of(
                    "success", false,
                    "message", "Timeout waiting for complaint data"
                ));
            }

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting complaint lifecycle process: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/resume-complaint-lifecycle/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeComplaintLifecycle(@PathVariable String processInstanceId, @PathVariable String signalType, @RequestBody Map<String, Object> workflowPayload) {
        System.out.println("WorkflowPayload: " + workflowPayload);
        System.out.println("ProcessInstanceId: " + processInstanceId);
        System.out.println("SignalType: " + signalType);

        try {
            Map<String, Object> variables = new HashMap<>();

            String complaintId = (String) workflowPayload.get("complaintId");
            String role = (String) workflowPayload.get("role");
            String decision = (String) workflowPayload.get("decision");

            if (complaintId != null) {
                variables.put("complaintId", complaintId);
            }
            if (role != null) {
                variables.put("role", role);
            }
            if (decision != null) {
                if ("NotifyAgent".equals(signalType)) {
                    // Signal to notify agent
                    runtimeService.signalEventReceived("NotifyAgent", processInstanceId, variables);
                } else if ("Escalated".equals(signalType)) {
                    // Signal for escalation
                    variables.put("agentDecision", "escalate");
                    runtimeService.signalEventReceived("Escalated", processInstanceId, variables);
                } else if ("AgentDecision".equals(signalType)) {
                    // Set agent decision
                    variables.put("agentDecision", decision);
                    runtimeService.setVariables(processInstanceId, variables);
                } else if ("EscalationDecision".equals(signalType)) {
                    // Set escalation decision
                    variables.put("escalationDecision", decision);
                    runtimeService.setVariables(processInstanceId, variables);
                }
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Complaint lifecycle process resumed with signal type " + signalType
            ));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error resuming complaint lifecycle process: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/resume-complaint-process/{processInstanceId}/{signalType}")
    public ResponseEntity<?> resumeComplaintProcess(@PathVariable String processInstanceId, @PathVariable String signalType, @RequestBody Map<String, Object> WorkFlowPayload) {
        String activityId;
        String signalName;

        System.out.println("WorkFlowPayload: " + WorkFlowPayload);
        System.out.println("ProcessInstanceId: " + processInstanceId);
        System.out.println("SignalType: " + signalType);
        
        // Map signalType to the appropriate activity ID and signal name
        switch (signalType) {
            case "Agent_action":
                activityId = "AgentAction";
                signalName = "ProcessSignal_AgentAction";
                break;
            case "Officer_action":
                activityId = "OfficerAction";
                signalName = "ProcessSignal_OfficerAction";
                break;
            case "Manager_action":
                activityId = "ManagerAction";
                signalName = "ProcessSignal_ManagerAction";
                break;
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of(
                        "success", false,
                        "message", "Invalid signal type: " + signalType + ". Valid types are Agent_action, Officer_action, or Manager_action."
                    )
                );
        }
        
        // Find the execution that is waiting at the specified signal catch event
        Execution execution = runtimeService.createExecutionQuery()
            .processInstanceId(processInstanceId)
            .activityId(activityId)
            .singleResult();
        
        if (execution == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                Map.of(
                    "success", false,
                    "message", "No execution found waiting at " + activityId + " for process: " + processInstanceId
                )
            );
        }
        
        System.out.println("[Process: " + processInstanceId + "] Resuming with signal " + signalName + " at " + activityId);
        String executionId = execution.getId();

        Map<String, Object> variables = new HashMap<>();
        
        String complaintId = (String) WorkFlowPayload.get("complaintId");
        String applicationType = (String) WorkFlowPayload.get("applicationType");
        String role = (String) WorkFlowPayload.get("role");
        
        if (complaintId != null) {
            variables.put("complaintId", complaintId);
        }

        if(role != null) {
            variables.put("role", role);
        }
        if (applicationType != null) {
            variables.put("applicationType", applicationType);
        }
        
        runtimeService.signalEventReceived(signalName, executionId, variables);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Complaint process " + processInstanceId + " resumed with signal type " + signalType
        ));
    }

    @PostMapping("/complaint-lifecycle/start/{complaintId}")
    public ResponseEntity<?> startComplaintLifecycleProcess(@PathVariable String complaintId) {
        try {
            Map<String, Object> variables = Map.of(
                "complaintId", complaintId
            );
            log.info("Starting complaint lifecycle process for complaint ID: {}", complaintId);

            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("complaintLifecycleProcess", variables);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Complaint lifecycle process started successfully",
                "processInstanceId", processInstance.getId(),
                "complaintId", complaintId
            ));

        } catch (Exception e) {
            log.error("Error starting complaint lifecycle process for complaint ID: {}", complaintId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "Failed to start complaint lifecycle process: " + e.getMessage(),
                    "complaintId", complaintId
                ));
        }
    }

    @PostMapping("/complaint-lifecycle/signal/{processInstanceId}")
    public ResponseEntity<?> sendComplaintLifecycleSignal(
            @PathVariable String processInstanceId,
            @RequestBody Map<String, Object> payload) {
        try {
            String signalName = (String) payload.get("signalName");
            String agentDecision = (String) payload.get("agentDecision");
            String escalationDecision = (String) payload.get("escalationDecision");

            log.info("Sending signal {} to complaint lifecycle process: {}", signalName, processInstanceId);

            Map<String, Object> variables = new HashMap<>();

            if (agentDecision != null) {
                variables.put("agentDecision", agentDecision);
            }

            if (escalationDecision != null) {
                variables.put("escalationDecision", escalationDecision);
            }

            // Send the signal
            if ("NotifyAgent".equals(signalName)) {
                runtimeService.signalEventReceived("NotifyAgent", processInstanceId, variables);
            } else if ("Escalated".equals(signalName)) {
                runtimeService.signalEventReceived("Escalated", processInstanceId, variables);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of(
                        "success", false,
                        "message", "Invalid signal name. Valid signals: NotifyAgent, Escalated"
                    ));
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Signal sent successfully",
                "processInstanceId", processInstanceId,
                "signalName", signalName
            ));

        } catch (Exception e) {
            log.error("Error sending signal to complaint lifecycle process {}", processInstanceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "Failed to send signal: " + e.getMessage(),
                    "processInstanceId", processInstanceId
                ));
        }
    }

    @GetMapping("/complaint-lifecycle/status/{processInstanceId}")
    public ResponseEntity<?> getComplaintLifecycleStatus(@PathVariable String processInstanceId) {
        try {
            log.info("Getting complaint lifecycle status for process: {}", processInstanceId);

            // Get process instance
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of(
                        "success", false,
                        "message", "Process instance not found",
                        "processInstanceId", processInstanceId
                    ));
            }

            // Get process variables
            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);

            // Get current activities
            List<String> activeActivities = runtimeService.getActiveActivityIds(processInstanceId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Complaint lifecycle status retrieved successfully",
                "processInstanceId", processInstanceId,
                "processDefinitionKey", processInstance.getProcessDefinitionKey(),
                "activeActivities", activeActivities,
                "variables", variables,
                "isEnded", processInstance.isEnded()
            ));

        } catch (Exception e) {
            log.error("Error getting complaint lifecycle status for process {}", processInstanceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "message", "Failed to get status: " + e.getMessage(),
                    "processInstanceId", processInstanceId
                ));
        }
    }
}



