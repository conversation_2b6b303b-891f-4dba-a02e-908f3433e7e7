package bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "workplace_training_plan")
@Getter
@Setter
@ToString
public class WorkPlaceTrainingPlan {

    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "uuid2")
    @Column(name = "id", columnDefinition = "uuid")
    private UUID id;

    @Column(name = "user_id", columnDefinition = "uuid")
    private UUID userId;

    @Column(name = "organisation_id", nullable = false)
    private UUID organisationId;

    @Column(name = "application_status")
    private String applicationStatus;

    @Column(name = "application_state")
    private String applicationState;
    
    @Column(name = "application_number")
    private String applicationNumber;

    @Column(name = "reference_number")
    private String referenceNumber;

    // New fields
    @Column(name = "financial_year")
    private String financialYear;

    @Column(name = "location")
    private String location;

    @Column(name = "contact_date")
    private LocalDate contactDate;

    @Column(name = "submission_date")
    private LocalDate submissionDate;

    // Role-based workflow fields
    @Column(name = "assigned_agent")
    private String assignedAgent;

    @Column(name = "assigned_agent_lead")
    private String assignedAgentLead;

    @Column(name = "assigned_officer_lead")
    private String assignedOfficerLead;

    @Column(name = "assigned_officer")
    private String assignedOfficer;

    @Column(name = "assigned_manager")
    private String assignedManager;

    // Soft delete fields
    @Column(name = "deleted")
    private Boolean deleted = false;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "deleted_date")
    private Date deletedDate;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CourseDetails> courseDetails = new ArrayList<>();

    // Audit fields
    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "last_modified_date")
    private LocalDateTime lastModifiedDate;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @PrePersist
    protected void onCreate() {
        createdDate = LocalDateTime.now();
        lastModifiedDate = LocalDateTime.now();
        deleted = false;
        
        // Set submission date to current date if not already set
        if (submissionDate == null) {
            submissionDate = LocalDate.now();
        }

        // Set contact date to current date if not already set
        if (contactDate == null) {
            contactDate = LocalDate.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        lastModifiedDate = LocalDateTime.now();
    }

    
    @Column(name = "process_instance_id", nullable = true)
    private String processInstanceId;
}
