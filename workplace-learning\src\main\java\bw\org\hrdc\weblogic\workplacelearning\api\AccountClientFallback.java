package bw.org.hrdc.weblogic.workplacelearning.api;

import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;


@Component
public class AccountClientFallback implements AccountClient {

    @Override
    public ApiResponse<?> getAllAgents() {
        return new ApiResponse<>(false, "Account service is currently unavailable", new ArrayList<>(), null);
    }

    @Override
    public ApiResponse<?> getAgentLeads() {
        return new ApiResponse<>(false, "Account service is currently unavailable", new ArrayList<>(), null);
    }

    @Override
    public ApiResponse<?> getAgentLead(String agentId) {
        return new ApiResponse<>(false, "Account service is currently unavailable", null, null);
    }
}
