package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 29/03/25 06:42
 * @UpdatedBy martinspectre
 * @UpdatedOn 29/03/25 06:42
 */
@Data
public class ComplaintSearchCriteria {
    private String status;
    private String sortBy = "name";
    private String direction = "ASC";
    private String assignedTo;
    private String role; // ✅ Added role field for role-based filtering
    private List<Enums.ComplaintState> complaintStates;
}
