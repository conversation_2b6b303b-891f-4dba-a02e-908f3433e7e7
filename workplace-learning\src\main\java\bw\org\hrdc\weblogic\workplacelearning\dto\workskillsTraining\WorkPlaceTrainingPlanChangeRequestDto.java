package bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WorkPlaceTrainingPlanChangeRequestDto {

    private Long id;
    private String uuid;
    private String assignedAgent;
    private String assignedAgentLead;
    private String assignedOfficerLead;
    private String assignedOfficer;
    private String assignedManager;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private boolean isDeleted;
    private String organisationId;
    private WorkPlaceTrainingPlan trainingPlan;
    private String applicationStatus;
    private String applicationState;
    private String applicationNumber;
    private String referenceNumber;
    private String financialYear;
    private String location;
    private LocalDate contactDate;
    private LocalDate submissionDate;
    private List<CourseDetailsDto> courseDetails;
    private String processInstanceId;
    private String trainingPlanId;
}
