package com.workflowenginee.workflow.controller;

import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/complaint-workflow")
public class ComplaintWorkflowController {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RestTemplate restTemplate;

    @PostMapping("/start/{complaintId}")
    public ResponseEntity<?> startComplaintWorkflow(@PathVariable String complaintId) {
        try {
            log.info("Starting complaint workflow for complaint ID: {}", complaintId);

            // Start the complaint lifecycle process
            Map<String, Object> variables = new HashMap<>();
            variables.put("complaintId", complaintId);

            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("complaintLifecycleProcess", variables);

            // Update the complaint with the process instance ID
            updateComplaintWithProcessId(complaintId, processInstance.getId());

            // Send signal to notify agent
            sendNotifyAgentSignal(processInstance.getId());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Complaint workflow started successfully",
                "processInstanceId", processInstance.getId(),
                "complaintId", complaintId
            ));

        } catch (Exception e) {
            log.error("Error starting complaint workflow: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error starting complaint workflow: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/agent-decision/{processInstanceId}")
    public ResponseEntity<?> submitAgentDecision(@PathVariable String processInstanceId, 
                                                @RequestBody Map<String, Object> decisionPayload) {
        try {
            String agentDecision = (String) decisionPayload.get("agentDecision");
            String agentComments = (String) decisionPayload.get("agentComments");
            String complaintId = (String) decisionPayload.get("complaintId");

            log.info("Processing agent decision for process {}: {}", processInstanceId, agentDecision);

            // Set variables for the workflow
            Map<String, Object> variables = new HashMap<>();
            variables.put("agentDecision", agentDecision);
            variables.put("agentComments", agentComments);
            variables.put("complaintId", complaintId);

            // Set the variables in the process
            runtimeService.setVariables(processInstanceId, variables);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Agent decision submitted successfully",
                "processInstanceId", processInstanceId,
                "decision", agentDecision
            ));

        } catch (Exception e) {
            log.error("Error submitting agent decision: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error submitting agent decision: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/escalate/{processInstanceId}")
    public ResponseEntity<?> escalateComplaint(@PathVariable String processInstanceId,
                                              @RequestBody Map<String, Object> escalationPayload) {
        try {
            String escalationDecision = (String) escalationPayload.get("escalationDecision");
            String escalationReason = (String) escalationPayload.get("escalationReason");
            String escalatedBy = (String) escalationPayload.get("escalatedBy");

            log.info("Processing escalation for process {}: {}", processInstanceId, escalationDecision);

            // Set variables for the workflow
            Map<String, Object> variables = new HashMap<>();
            variables.put("escalationDecision", escalationDecision);
            variables.put("escalationReason", escalationReason);
            variables.put("escalatedBy", escalatedBy);

            // Set the variables and send escalation signal
            runtimeService.setVariables(processInstanceId, variables);
            runtimeService.signalEventReceived("Escalated", processInstanceId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Complaint escalated successfully",
                "processInstanceId", processInstanceId,
                "escalationDecision", escalationDecision
            ));

        } catch (Exception e) {
            log.error("Error escalating complaint: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error escalating complaint: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/status/{processInstanceId}")
    public ResponseEntity<?> getWorkflowStatus(@PathVariable String processInstanceId) {
        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of(
                    "success", false,
                    "message", "Process instance not found: " + processInstanceId
                ));
            }

            Map<String, Object> variables = runtimeService.getVariables(processInstanceId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "processInstanceId", processInstanceId,
                "isEnded", processInstance.isEnded(),
                "variables", variables
            ));

        } catch (Exception e) {
            log.error("Error getting workflow status: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Error getting workflow status: " + e.getMessage()
            ));
        }
    }

    private void updateComplaintWithProcessId(String complaintId, String processInstanceId) {
        try {
            Map<String, Object> updatePayload = new HashMap<>();
            updatePayload.put("processInstanceId", processInstanceId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(updatePayload, headers);

            String updateUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/process-instance";
            
            restTemplate.exchange(updateUrl, HttpMethod.PUT, entity, Map.class);
            
            log.info("Updated complaint {} with process instance ID {}", complaintId, processInstanceId);
            
        } catch (Exception e) {
            log.warn("Failed to update complaint with process instance ID: {}", e.getMessage());
        }
    }

    private void sendNotifyAgentSignal(String processInstanceId) {
        try {
            runtimeService.signalEventReceived("NotifyAgent", processInstanceId);
            log.info("Sent NotifyAgent signal to process {}", processInstanceId);
        } catch (Exception e) {
            log.warn("Failed to send NotifyAgent signal: {}", e.getMessage());
        }
    }
}
