package com.workflowenginee.workflow.delegate;

import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.netflix.discovery.shared.Application;
import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;

@Component("fetchBackOfficeDataDelegate")
public class FetchBackOfficeData implements JavaDelegate {

     private final WorkplaceLearningClient workplaceLearningClient;

    @Autowired
    public FetchBackOfficeData(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

     @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = (String) execution.getVariable("processInstanceId");
        String applicationNumber = (String) execution.getVariable("referenceNumber");
        String applicationType = (String) execution.getVariable("applicationType");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Fetching application...");
        System.out.println("Role: " + role);

         ApiResponse<?> response = null;
        // Check application type
        if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType)) {
           

            try {
                // Call external service
                response = workplaceLearningClient.getApplicationByReferenceNumber(applicationNumber);
                System.out.println("[Process: " + processInstanceId + "] Response: " + response);

                if (response != null && response.getData() instanceof Map) {
                try {
                    Map<String, Object> responseData = (Map<String, Object>) response.getData();
                    String finalStatus = null;

                    if (responseData.containsKey("application")) {
                        Map<String, Object> application = (Map<String, Object>) responseData.get("application");

                        String applicationState = (String) application.get("applicationState");
                        String applicationStatus = (String) application.get("applicationStatus");
                        String companyId = (String) application.get("organisationId");
                        String assignedOfficerName = (String) application.get("assignedOfficer");

                        System.out.println("companyId: " + companyId);
                        System.out.println("applicationState: " + applicationState);
                        System.out.println("applicationStatus: " + applicationStatus);

                        String currentActivityId = execution.getCurrentActivityId();
                        System.out.println("Current activity ID: " + currentActivityId);

                        // Determine status
                        if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                            Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";

                        } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                                Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                            System.out.println("Setting status to approved for manager notification");

                        } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "rejected";

                        } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "info-request";

                        } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                        }

                        // Set process variables
                        execution.setVariable("status", finalStatus);
                        execution.setVariable("role", role);
                        execution.setVariable("companyId", companyId);
                        execution.setVariable("assignedOfficerName", assignedOfficerName);
                        System.out.println("[Process: " + processInstanceId + "] Set status variable to: " + finalStatus);

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] 'application' key missing in response data.");
                        execution.setVariable("status", "pending");
                    }
                } catch (ClassCastException e) {
                    System.err.println("[Process: " + processInstanceId + "] Error parsing response data:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                } catch (Exception e) {
                    System.err.println("[Process: " + processInstanceId + "] Unexpected error during data processing:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                }
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty response data.");
                execution.setVariable("status", "pending");
            }

                
            } catch (Exception e) {
                System.err.println("[Process: " + processInstanceId + "] Error calling workplaceLearningClient:");
                e.printStackTrace();
                execution.setVariable("status", "error");
                return;
            }

            
        } else if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType)) {
            try {
                response = workplaceLearningClient.getApplicationById(applicationNumber);


                if (response != null && response.getData() instanceof Map) {
                try {
                    Map<String, Object> responseData = (Map<String, Object>) response.getData();
                    String finalStatus = null;

                    if (responseData.containsKey("application")) {
                        Map<String, Object> application = (Map<String, Object>) responseData.get("application");

                        String applicationState = (String) application.get("state");
                        String applicationStatus = (String) application.get("status");
                        String companyId = (String) application.get("organisationId");
                        String assignedOfficerName = (String) application.get("assignedOfficer");

                        System.out.println("companyId: " + companyId);
                        System.out.println("applicationState: " + applicationState);
                        System.out.println("applicationStatus: " + applicationStatus);

                        String currentActivityId = execution.getCurrentActivityId();
                        System.out.println("Current activity ID: " + currentActivityId);

                        // Determine status
                        if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                            Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";

                        } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                                Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                            System.out.println("Setting status to approved for manager notification");

                        } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "rejected";

                        } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "info-request";

                        } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                        }

                        // Set process variables
                        execution.setVariable("status", finalStatus);
                        execution.setVariable("role", role);
                        execution.setVariable("companyId", companyId);
                        execution.setVariable("assignedOfficerName", assignedOfficerName);
                        execution.setVariable("applicationType", applicationType);
                        execution.setVariable("applicationId", application.get("id"));
                        System.out.println("[Process: " + processInstanceId + "] Set status variable to: " + finalStatus);

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] 'application' key missing in response data.");
                        execution.setVariable("status", "pending");
                    }
                } catch (ClassCastException e) {
                    System.err.println("[Process: " + processInstanceId + "] Error parsing response data:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                } catch (Exception e) {
                    System.err.println("[Process: " + processInstanceId + "] Unexpected error during data processing:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                }
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty response data.");
                execution.setVariable("status", "pending");
            }
            } catch (Exception e) {
                System.err.println("[Process: " + processInstanceId + "] Error calling workplaceLearningClient:");
                e.printStackTrace();
                execution.setVariable("status", "error");
                return;
            }
        } else if (Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)){
            try {
                java.util.UUID applicationId = java.util.UUID.fromString(applicationNumber);
                response = workplaceLearningClient.getApplicationById(applicationId);

                if (response != null && response.getData() instanceof Map) {
                try {
                    Map<String, Object> responseData = (Map<String, Object>) response.getData();
                    String finalStatus = null;

                    if (responseData.containsKey("application")) {
                        Map<String, Object> application = (Map<String, Object>) responseData.get("application");

                        String applicationState = (String) application.get("applicationState");
                        String applicationStatus = (String) application.get("applicationStatus");
                        String companyId = (String) application.get("organisationId");
                        String assignedOfficerName = (String) application.get("assignedOfficer");

                        System.out.println("companyId: " + companyId);
                        System.out.println("applicationState: " + applicationState);
                        System.out.println("applicationStatus: " + applicationStatus);

                        String currentActivityId = execution.getCurrentActivityId();
                        System.out.println("Current activity ID: " + currentActivityId);

                        // Determine status
                        if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                            Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";

                        } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                                Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                            System.out.println("Setting status to approved for manager notification");

                        } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "rejected";

                        } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "info-request";

                        } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                        }

                        // Set process variables
                        execution.setVariable("status", finalStatus);
                        execution.setVariable("role", role);
                        execution.setVariable("companyId", companyId);
                        execution.setVariable("assignedOfficerName", assignedOfficerName);
                        execution.setVariable("applicationType", applicationType);
                        execution.setVariable("applicationId", application.get("id"));
                        System.out.println("[Process: " + processInstanceId + "] Set status variable to: " + finalStatus);

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] 'application' key missing in response data.");
                        execution.setVariable("status", "pending");
                    }
                } catch (ClassCastException e) {
                    System.err.println("[Process: " + processInstanceId + "] Error parsing response data:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                } catch (Exception e) {
                    System.err.println("[Process: " + processInstanceId + "] Unexpected error during data processing:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                }
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty response data.");
                execution.setVariable("status", "pending");
            }
                // response = workplaceLearningClient.getApplicationById(applicationNumber);
            } catch (Exception e) {
                System.err.println("[Process: " + processInstanceId + "] Error calling workplaceLearningClient:");
                e.printStackTrace();
                execution.setVariable("status", "error");
                return;
            }
        } else if (Enums.ApplicationType.NOC.name().equalsIgnoreCase(applicationType)) {
           try {
                // Call external service
                response = workplaceLearningClient.searchByReferenceNumber(applicationNumber);

                if (response != null && response.getData() instanceof Map) {
                try {
                    Map<String, Object> responseData = (Map<String, Object>) response.getData();
                    String finalStatus = null;

                    if (responseData.containsKey("application")) {
                        Map<String, Object> application = (Map<String, Object>) responseData.get("application");

                        String applicationState = (String) application.get("applicationState");
                        String applicationStatus = (String) application.get("applicationStatus");
                        String companyId = (String) application.get("organisationId");
                        String assignedOfficerName = (String) application.get("assignedOfficer");

                        System.out.println("companyId: " + companyId);
                        System.out.println("applicationState: " + applicationState);
                        System.out.println("applicationStatus: " + applicationStatus);

                        String currentActivityId = execution.getCurrentActivityId();
                        System.out.println("Current activity ID: " + currentActivityId);

                        // Determine status
                        if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                            Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";

                        } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                                Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                            System.out.println("Setting status to approved for manager notification");

                        } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "rejected";

                        } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "info-request";

                        } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                            finalStatus = "approved";
                        }

                        // Set process variables
                        execution.setVariable("status", finalStatus);
                        execution.setVariable("role", role);
                        execution.setVariable("companyId", companyId);
                        execution.setVariable("assignedOfficerName", assignedOfficerName);
                        System.out.println("[Process: " + processInstanceId + "] Set status variable to: " + finalStatus);

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] 'application' key missing in response data.");
                        execution.setVariable("status", "pending");
                    }
                } catch (ClassCastException e) {
                    System.err.println("[Process: " + processInstanceId + "] Error parsing response data:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                } catch (Exception e) {
                    System.err.println("[Process: " + processInstanceId + "] Unexpected error during data processing:");
                    e.printStackTrace();
                    execution.setVariable("status", "error");
                }
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty response data.");
                execution.setVariable("status", "pending");
            }

                
            } catch (Exception e) {
                System.err.println("[Process: " + processInstanceId + "] Error calling workplaceLearningClient:");
                e.printStackTrace();
                execution.setVariable("status", "error");
                return;
            }
        }
         else {
            System.out.println("[Process: " + processInstanceId + "] Unsupported application type: " + applicationType);
            execution.setVariable("status", "skipped");
        }

    }

}
