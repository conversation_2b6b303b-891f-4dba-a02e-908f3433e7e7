package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationConstants;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.dto.BatchStatusUpdateResult;
import bw.org.hrdc.weblogic.workplacelearning.dto.common.CompanyDTO;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.CourseDetailsDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;
import bw.org.hrdc.weblogic.workplacelearning.entity.Batch;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.mapper.workplaceTraining.WorkPlaceTrainingPlanMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.BatchRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.CourseDetailsRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanSpecification;
import bw.org.hrdc.weblogic.workplacelearning.service.IWorkPlaceTrainingPlanService;
import bw.org.hrdc.weblogic.workplacelearning.service.WorkPlaceTrainingPlanCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;

import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.text.ParseException;

/**
 * Implementation of the service interface for WorkPlaceTrainingPlan.
 */
@Service
@AllArgsConstructor
public class WorkPlaceTrainingPlanServiceImpl implements IWorkPlaceTrainingPlanService {

    private static final Logger log = LoggerFactory.getLogger(WorkPlaceTrainingPlanServiceImpl.class);

    private final WorkPlaceTrainingPlanRepository trainingPlanRepository;
    private final WorkPlaceTrainingPlanMapper trainingPlanMapper;

    @Autowired
    private CompanyClient companyClient;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private CourseDetailsRepository courseDetailsRepository;

    @Autowired
    private WorkPlaceTrainingPlanCommentsService commentsService;

    @Override
    @Transactional
    public WorkPlaceTrainingPlanResponseDto createOrUpdateTrainingPlan(WorkPlaceTrainingPlanDto trainingPlanDto, boolean isDraft) {
        try {
            log.info("Processing create/update request for workplace training plan, isDraft: {}", isDraft);
            
            // Check if this is an update or create operation
            if (trainingPlanDto.getId() != null) {
                log.info("Updating existing training plan with ID: {}, isDraft: {}", trainingPlanDto.getId(), isDraft);
                
                WorkPlaceTrainingPlan existingPlan = trainingPlanRepository.findById(trainingPlanDto.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Training Plan not found with ID: " + trainingPlanDto.getId()));

                String currentApplicationNumber = existingPlan.getApplicationNumber();
                String currentReferenceNumber = existingPlan.getReferenceNumber();
                LocalDate currentSubmissionDate = existingPlan.getSubmissionDate();
                String currentApplicationState = existingPlan.getApplicationState();
                                
                log.info("Current application state: {}, status: {}", currentApplicationState);

                if (isDraft) {
                    log.info("Updating as draft application");
                    trainingPlanDto.setApplicationState(Enums.State.DRAFT.name());
                    trainingPlanDto.setApplicationStatus(Enums.Status.INITIAL.name());
                } else {
                    log.info("Updating as submitted application");
                    
                    // Always update status to PENDING for non-draft submissions
                    trainingPlanDto.setApplicationStatus(Enums.Status.PENDING.name());
                    
                    // Only change state if it's a draft being converted to submitted
                    if (Enums.State.DRAFT.name().equals(currentApplicationState)) {
                        if (currentReferenceNumber == null || currentReferenceNumber.isEmpty()) {
                            log.info("Converting draft to submitted application - generating reference number");
                            String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("REF");
                            trainingPlanDto.setApplicationState(Enums.State.SUBMITTED.name());
                            trainingPlanDto.setReferenceNumber(referenceNumber);
                            log.info("Generated reference number: {} for application ID: {}", referenceNumber, trainingPlanDto.getId());
                         } 
                    } else {
                        // Preserve existing state for non-draft applications
                        log.info("Preserving existing application state: {}", currentApplicationState);
                        trainingPlanDto.setApplicationState(currentApplicationState);
                        trainingPlanDto.setReferenceNumber(currentReferenceNumber);
                    }
                }
                
                trainingPlanDto.setApplicationNumber(currentApplicationNumber);

                if (trainingPlanDto.getSubmissionDate() == null) {
                    trainingPlanDto.setSubmissionDate(currentSubmissionDate);
                }

                updateTrainingPlan(trainingPlanDto);

                WorkPlaceTrainingPlan updatedPlan = trainingPlanRepository.findById(trainingPlanDto.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Training Plan not found after update with ID: " + trainingPlanDto.getId()));

                return WorkPlaceTrainingPlanResponseDto.builder()
                        .statusCode(ApplicationConstants.STATUS_200)
                        .statusMsg("Workplace Training Plan updated successfully.")
                        .applicationId(updatedPlan.getId())
                        .state(updatedPlan.getApplicationState())
                        .status(updatedPlan.getApplicationStatus())
                        .applicationNumber(updatedPlan.getApplicationNumber())
                        .referenceNumber(updatedPlan.getReferenceNumber())
                        .build();
                
            } else {
                if (isDraft) {
                    log.info("Creating draft workplace training plan");

                    trainingPlanDto.setApplicationState(Enums.State.DRAFT.name());
                    trainingPlanDto.setApplicationStatus(Enums.Status.INITIAL.name());

                    String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("APP");
                    trainingPlanDto.setApplicationNumber(applicationNumber);
                    log.info("Generated application number: {}", applicationNumber);
                    
                } else {
                    log.info("Creating submitted workplace training plan");
                    trainingPlanDto.setApplicationState(Enums.State.SUBMITTED.name());
                    trainingPlanDto.setApplicationStatus(Enums.Status.PENDING.name());

                    String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("APP");
                    String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("REF");
                    
                    trainingPlanDto.setApplicationNumber(applicationNumber);
                    trainingPlanDto.setReferenceNumber(referenceNumber);
                    
                    log.info("Generated application number: {} and reference number: {}", applicationNumber, referenceNumber);
                }

                if (trainingPlanDto.getSubmissionDate() == null && !isDraft) {
                    trainingPlanDto.setSubmissionDate(LocalDate.now());
                }

                WorkPlaceTrainingPlan trainingPlan = WorkPlaceTrainingPlanMapper.toEntity(trainingPlanDto);

                if (trainingPlan.getCourseDetails() != null) {
                    trainingPlan.getCourseDetails().forEach(detail -> detail.setApplication(trainingPlan));
                }

                WorkPlaceTrainingPlan savedPlan = trainingPlanRepository.save(trainingPlan);
                log.info("WorkPlaceTrainingPlan created with ID: {}", savedPlan.getId());

                return WorkPlaceTrainingPlanResponseDto.builder()
                        .statusCode(ApplicationConstants.STATUS_201)
                        .statusMsg("Workplace Training Plan created successfully.")
                        .applicationId(savedPlan.getId())
                        .state(savedPlan.getApplicationState())
                        .status(savedPlan.getApplicationStatus())
                        .applicationNumber(savedPlan.getApplicationNumber())
                        .referenceNumber(savedPlan.getReferenceNumber())
                        .build();
            }
        } catch (ResourceNotFoundException e) {
            log.error("Resource not found: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error processing create/update request: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create or update workplace training plan", e);
        }
    }

    @Override
    @Transactional
    public void updateTrainingPlan(WorkPlaceTrainingPlanDto trainingPlanDto) {

        WorkPlaceTrainingPlan existingPlan = trainingPlanRepository.findById(trainingPlanDto.getId())
                .orElseThrow(() -> new ApplicationNotFoundException("Training Plan not found with ID: " + trainingPlanDto.getId()));

        String currentApplicationNumber = existingPlan.getApplicationNumber();
        String currentReferenceNumber = existingPlan.getReferenceNumber();
        LocalDate currentSubmissionDate = existingPlan.getSubmissionDate();
        String currentApplicationState = existingPlan.getApplicationState();

        // Ensure DTO has the correct state (we want to preserve state but not status)
        if (trainingPlanDto.getApplicationState() == null) {
            log.info("Setting application state from existing plan: {}", currentApplicationState);
            trainingPlanDto.setApplicationState(currentApplicationState);
        }
        
        
        WorkPlaceTrainingPlan updatedPlan = trainingPlanMapper.toEntity(trainingPlanDto);
        log.info("Updated plan from mapper - application state: {}, status: {}", updatedPlan.getApplicationState(), updatedPlan.getApplicationStatus());

        updatedPlan.setId(existingPlan.getId());

        if (updatedPlan.getApplicationNumber() == null || updatedPlan.getApplicationNumber().isEmpty()) {
            updatedPlan.setApplicationNumber(currentApplicationNumber);
        }
        
        if (updatedPlan.getReferenceNumber() == null || updatedPlan.getReferenceNumber().isEmpty()) {
            updatedPlan.setReferenceNumber(currentReferenceNumber);
        }
        
        if (updatedPlan.getSubmissionDate() == null) {
            updatedPlan.setSubmissionDate(currentSubmissionDate);
        }

        if (updatedPlan.getCourseDetails() != null) {
            existingPlan.getCourseDetails().clear();
            updatedPlan.getCourseDetails().forEach(detail -> {
                detail.setApplication(existingPlan);
                existingPlan.getCourseDetails().add(detail);
            });
        }

        existingPlan.setApplicationStatus(updatedPlan.getApplicationStatus());
        existingPlan.setApplicationState(updatedPlan.getApplicationState());
        existingPlan.setOrganisationId(updatedPlan.getOrganisationId());
        existingPlan.setUserId(updatedPlan.getUserId());
        existingPlan.setFinancialYear(updatedPlan.getFinancialYear());
        existingPlan.setLocation(updatedPlan.getLocation());
        existingPlan.setContactDate(updatedPlan.getContactDate());
        existingPlan.setSubmissionDate(updatedPlan.getSubmissionDate());
        existingPlan.setReferenceNumber(updatedPlan.getReferenceNumber());
        existingPlan.setApplicationNumber(updatedPlan.getApplicationNumber());
        existingPlan.setLastModifiedDate(LocalDateTime.now());
        trainingPlanRepository.save(existingPlan);

        log.info("WorkPlaceTrainingPlan updated with ID: {}", existingPlan.getId());
    }

    @Override
    public WorkPlaceTrainingPlanDto getTrainingPlan(UUID id) {
        log.info("Fetching workplace training plan by ID: {}", id);

        WorkPlaceTrainingPlan trainingPlan = trainingPlanRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("WorkPlace Training Plan not found with ID: " + id));
        
        WorkPlaceTrainingPlanDto dto = new WorkPlaceTrainingPlanDto();
        
        try {
            // 1. Fetch company details
            if (trainingPlan.getOrganisationId() != null) {
                ApiResponse<?> companyResponse = companyClient.fetchCompanyById(trainingPlan.getOrganisationId().toString());
                if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                    Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                    dto.setCompany(companyData);
                } else {
                    log.warn("Company data not available for WorkPlaceTrainingPlan id: {}, Company id: {}",
                        id, trainingPlan.getOrganisationId());
                }
            }
            
            // 2. Set Course Details
            if (trainingPlan.getCourseDetails() != null && !trainingPlan.getCourseDetails().isEmpty()) {
                List<CourseDetailsDto> courseDetailsDtos = trainingPlan.getCourseDetails().stream()
                    .map(course -> {
                        CourseDetailsDto courseDto = new CourseDetailsDto();
                        courseDto.setId(course.getId());
                        courseDto.setProgramme(course.getProgramme());
                        courseDto.setSkills(course.getSkills());
                        courseDto.setTrainingStartDate(course.getTrainingStartDate());
                        courseDto.setTrainingEndDate(course.getTrainingEndDate());
                        courseDto.setInstitution(course.getInstitution());
                        courseDto.setLocation(course.getLocation());
                        courseDto.setAccreditingBody(course.getAccreditingBody());
                        courseDto.setLevelOfTraining(course.getLevelOfTraining());
                        courseDto.setCostOfTraining(course.getCostOfTraining());
                        courseDto.setNoncitizens(course.getNoncitizens());
                        courseDto.setCitizens(course.getCitizens());
                        courseDto.setPeopleTrained(course.getPeopleTrained());
                        return courseDto;
                    })
                    .collect(Collectors.toList());
                dto.setCourseDetails(courseDetailsDtos);
            }
            
            // 3. Map basic fields
            dto.setId(trainingPlan.getId());
            dto.setUserId(trainingPlan.getUserId());
            dto.setOrganisationId(trainingPlan.getOrganisationId());
            dto.setApplicationNumber(trainingPlan.getApplicationNumber());
            dto.setReferenceNumber(trainingPlan.getReferenceNumber());
            dto.setApplicationStatus(trainingPlan.getApplicationStatus());
            dto.setApplicationState(trainingPlan.getApplicationState());
            dto.setFinancialYear(trainingPlan.getFinancialYear());
            dto.setLocation(trainingPlan.getLocation());
            dto.setContactDate(trainingPlan.getContactDate());
            dto.setSubmissionDate(trainingPlan.getSubmissionDate());
            
            // 4. Map assignment fields
            dto.setAssignedAgent(trainingPlan.getAssignedAgent());
            dto.setAssignedAgentLead(trainingPlan.getAssignedAgentLead());
            dto.setAssignedOfficerLead(trainingPlan.getAssignedOfficerLead());
            dto.setAssignedOfficer(trainingPlan.getAssignedOfficer());
            dto.setAssignedManager(trainingPlan.getAssignedManager());
            
            // 5. Map deletion fields
            dto.setDeleted(trainingPlan.getDeleted());
            dto.setDeletedDate(trainingPlan.getDeletedDate());
            
            // 6. Map audit fields
            dto.setCreatedDate(trainingPlan.getCreatedDate());
            dto.setLastModifiedDate(trainingPlan.getLastModifiedDate());
            dto.setCreatedBy(trainingPlan.getCreatedBy());
            dto.setLastModifiedBy(trainingPlan.getLastModifiedBy());
            
        } catch (Exception e) {
            log.error("Error fetching details for training plan: {}", e.getMessage(), e);
        }
        
        return dto;
    }

    @Override
    public Map<String, Object> getTrainingPlansByRole(
            String role, UUID userId, UUID organisationId, String applicationStatus, String applicationState,
            Date submissionDate, String programme, Date startDate, Date endDate,
            String search, String referenceNumber, String companyName, int pageNumber, int size) {
        
        log.info("Service: Fetching training plans with criteria - role: {}, userId: {}, organisationId: {}, status: {}, state: {}, programme: {}, referenceNumber: {}, search: {}",
                role, userId, organisationId, applicationStatus, applicationState, programme, referenceNumber, search);
        
        // Return empty result for invalid role or userId
        if ((role != null && !isValidRole(role)) || (userId != null && !isValidUserId(userId))) {
            return createEmptyResponse(pageNumber, size);
        }
        
        // Build base specification for role and company filtering only
        Specification<WorkPlaceTrainingPlan> baseRoleSpec = buildBaseRoleSpecification(role, userId, organisationId);
        
        // Build full specification with all search criteria
        Specification<WorkPlaceTrainingPlan> fullSpec = buildTrainingPlanSpecification(
                role, userId, organisationId, applicationStatus, applicationState, submissionDate, 
                programme, startDate, endDate, search, referenceNumber, companyName);
       
               
        // Execute query with pagination
        Pageable pageable = PageRequest.of(pageNumber, size, Sort.by("createdDate").descending());
        Page<WorkPlaceTrainingPlan> trainingPlans = trainingPlanRepository.findAll(fullSpec, pageable);
        
        if (trainingPlans.isEmpty()) {
            return createEmptyResponse(pageNumber, size);
        }
        
        // Convert entities to DTOs
        List<Map<String, Object>> trainingPlanDtos = trainingPlans.getContent().stream()
                .map(this::convertToListDto)
                .collect(Collectors.toList());
        
        // Build response
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> metadata = createMetadata(trainingPlans, baseRoleSpec);
        response.put("metadata", metadata);
        response.put("content", trainingPlanDtos);
        
        return response;
    }
    
    /**
     * Builds a specification for filtering training plans based on various criteria.
     */
    private Specification<WorkPlaceTrainingPlan> buildTrainingPlanSpecification(
            String role, UUID userId, UUID organisationId, String applicationStatus, String applicationState, 
            Date submissionDate, String programme, Date startDate, Date endDate,
            String search, String referenceNumber, String companyName) {
        
        log.info("Building specification with role: {}, userId: {}, organisationId: {}, applicationStatus: {}, applicationState: {}, referenceNumber: {}", 
                 role, userId, organisationId, applicationStatus, applicationState, referenceNumber);
        
        // First get the base role specification
        Specification<WorkPlaceTrainingPlan> baseSpec = buildBaseRoleSpecification(role, userId, organisationId);

        // Then add additional filters
        return (root, query, cb) -> {
            // Get predicates from base specification
            Predicate basePredicate = baseSpec.toPredicate(root, query, cb);
            
            // Create new list for additional predicates
            List<Predicate> additionalPredicates = new ArrayList<>();
                       
                       
            // Add date range filters for created date with proper time handling
            if (startDate != null && endDate != null) {
                // Convert dates to LocalDateTime for proper comparison
                LocalDateTime startDateTime = startDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()
                    .withHour(0).withMinute(0).withSecond(0);
                
                // Set endDate to end of day (23:59:59)
                LocalDateTime endDateTime = endDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()
                    .withHour(23).withMinute(59).withSecond(59);
                
                // Convert back to Date objects
                Date startOfDay = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
                Date endOfDay = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
                
                log.info("Filtering by date range: {} to {}", startOfDay, endOfDay);
                additionalPredicates.add(cb.between(root.get("createdDate"), startOfDay, endOfDay));
            } else if (startDate != null) {
                // Set startDate to start of day (00:00:00)
                LocalDateTime startDateTime = startDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()
                    .withHour(0).withMinute(0).withSecond(0);
                Date startOfDay = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
                
                log.info("Filtering by date from: {}", startOfDay);
                additionalPredicates.add(cb.greaterThanOrEqualTo(root.get("createdDate"), startOfDay));
            } else if (endDate != null) {
                // Set endDate to end of day (23:59:59)
                LocalDateTime endDateTime = endDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()
                    .withHour(23).withMinute(59).withSecond(59);
                Date endOfDay = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
                
                log.info("Filtering by date until: {}", endOfDay);
                additionalPredicates.add(cb.lessThanOrEqualTo(root.get("createdDate"), endOfDay));
            }
            
            
            
             if (companyName != null && !companyName.isEmpty()) {
                try {
                    // Use the search API with proper error handling
                    ApiResponse<?> companyResponse = companyClient.searchCompanyByName(companyName);
                    log.info("Company Search API Response for '{}': {}", companyName, companyResponse);
                    
                    if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                        List<Map<String, Object>> companies = (List<Map<String, Object>>) companyResponse.getData();
                        
                        if (!companies.isEmpty()) {
                            List<UUID> companyIds = companies.stream()
                                .map(company -> {
                                    try {
                                        String id = company.get("organizationId").toString();
                                        log.debug("Found matching company: {} with UUID: {}",
                                            company.get("name"), id);
                                        return UUID.fromString(id);
                                    } catch (Exception e) {
                                        log.warn("Invalid company UUID format for company: {}",
                                            company.get("name"), e);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!companyIds.isEmpty()) {
                                log.info("Found {} matching companies for name: {}",
                                    companyIds.size(), companyName);
                                additionalPredicates.add(root.get("organisationId").in(companyIds));
                            } else {
                                // No valid company IDs found - return no results
                                log.warn("No valid company IDs found for name: {}", companyName);
                                additionalPredicates.add(cb.equal(cb.literal(1), 0));
                            }
                        } else {
                            // No companies found - return no results
                            log.warn("No companies found matching name: {}", companyName);
                            additionalPredicates.add(cb.equal(cb.literal(1), 0));
                        }
                    } else {
                        log.warn("Company search API returned null or invalid response for name: {}",
                            companyName);
                        additionalPredicates.add(cb.equal(cb.literal(1), 0));
                    }
                } catch (Exception e) {
                    log.error("Error searching for company with name '{}': {}",
                        companyName, e.getMessage(), e);
                    additionalPredicates.add(cb.equal(cb.literal(1), 0));
                }
            }
            // Add status filter
             if (applicationStatus != null && !applicationStatus.isEmpty()) {
                String[] statuses = applicationStatus.split(",");
                if (statuses.length > 1) {
                    // For multiple statuses, create an OR condition with partial matching
                    Predicate statusPredicate = cb.disjunction();
                    for (String status : statuses) {
                        String trimmedStatus = status.trim().toLowerCase();
                        statusPredicate = cb.or(statusPredicate,
                            cb.like(cb.lower(root.get("applicationStatus")), "%" + trimmedStatus + "%"));
                    }
                    additionalPredicates.add(statusPredicate);
                } else {
                     // For single status, use partial match with case insensitive comparison
                     additionalPredicates.add(cb.like(cb.lower(root.get("applicationStatus")),
                        "%" + applicationStatus.toLowerCase() + "%"));
                }
            }

            // Add state filter
            if (applicationState != null && !applicationState.isEmpty()) {
                String[] states = applicationState.split(",");
                if (states.length > 1) {
                    // For multiple states, create an OR condition with partial matching
                    Predicate statePredicate = cb.disjunction();
                    for (String state : states) {
                        String trimmedState = state.trim().toLowerCase();
                        statePredicate = cb.or(statePredicate,
                            cb.like(cb.lower(root.get("applicationState")), "%" + trimmedState + "%"));
                    }
                    additionalPredicates.add(statePredicate);
                } else {
                    // For single state, use exact match instead of partial match
                    additionalPredicates.add(cb.equal(root.get("applicationState"), applicationState));
                }
            }
            
            // Add submission date filter
            if (submissionDate != null) {
                additionalPredicates.add(cb.equal(root.get("submissionDate"), submissionDate));
            }
            
            // Add programme filter - exact match on CourseDetails entity
            if (programme != null && !programme.isEmpty()) {
                Join<WorkPlaceTrainingPlan, CourseDetails> courseDetailsJoin = root.join("courseDetails", JoinType.LEFT);
                additionalPredicates.add(cb.equal(courseDetailsJoin.get("programme"), programme));
            }
            
            
            
            // Add unified search functionality
            if (search != null && !search.isEmpty()) {
                String searchTerm = "%" + search.toLowerCase() + "%";
                
                // Create a disjunction (OR) for all searchable fields
                Predicate searchPredicate = cb.disjunction();
                
                // Add reference number search to unified search as well
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(root.get("referenceNumber")), searchTerm));

                // Add programme search to unified search - need to join with CourseDetails
                Join<WorkPlaceTrainingPlan, CourseDetails> searchCourseDetailsJoin = root.join("courseDetails", JoinType.LEFT);
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(searchCourseDetailsJoin.get("programme")), searchTerm));
                    
                
                // Add date search - convert date to string format for partial matching
                try {
                    // Try to parse the search term as a date
                    SimpleDateFormat[] dateFormats = {
                        new SimpleDateFormat("yyyy-MM-dd"),
                        new SimpleDateFormat("dd/MM/yyyy"),  // Add support for DD/MM/YYYY format
                        new SimpleDateFormat("MM/dd/yyyy"),
                        new SimpleDateFormat("dd-MM-yyyy"),
                        new SimpleDateFormat("yyyy/MM/dd")
                    };
                    
                    Date searchDate = null;
                    for (SimpleDateFormat format : dateFormats) {
                        try {
                            format.setLenient(false);  // Strict parsing to avoid invalid dates
                            searchDate = format.parse(search.trim());
                            log.info("Successfully parsed date '{}' using format '{}'", 
                                search.trim(), format.toPattern());
                            break;
                        } catch (ParseException e) {
                            // Try next format
                        }
                    }
                    
                    if (searchDate != null) {
                        // Format the date in database format for comparison
                        SimpleDateFormat dbFormat = new SimpleDateFormat("yyyy-MM-dd");
                        String formattedDate = dbFormat.format(searchDate);
                        log.info("Searching for date: {}", formattedDate);
                        
                        // Convert to LocalDateTime for proper time handling
                        LocalDateTime startDateTime = searchDate.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime()
                            .withHour(0).withMinute(0).withSecond(0);
                        
                        LocalDateTime endDateTime = searchDate.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime()
                            .withHour(23).withMinute(59).withSecond(59);
                        
                        // Convert back to Date objects
                        Date startOfDay = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
                        Date endOfDay = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
                        
                        // Add date search to the predicate - search for exact day
                        searchPredicate = cb.or(searchPredicate,
                            cb.between(root.get("createdDate"), startOfDay, endOfDay)
                        );
                        
                        log.info("Added date search predicate for range: {} to {}", startOfDay, endOfDay);
                    }
                } catch (Exception e) {
                    log.debug("Search term '{}' is not a valid date format: {}", search, e.getMessage());
                }
                
                // Search by status and state using enum values directly
                // Check if search term matches any Status enum value
                try {
                    // Try to parse the search term as a Status enum
                    Enums.Status statusEnum = Enums.Status.valueOf(search.trim().toUpperCase());
                    log.info("Found exact Status enum match: {}", statusEnum.name());
                    searchPredicate = cb.or(searchPredicate, 
                        cb.equal(root.get("applicationStatus"), statusEnum.name()));
                } catch (IllegalArgumentException e) {
                    // Not a valid Status enum, try State enum
                    try {
                        Enums.State stateEnum = Enums.State.valueOf(search.trim().toUpperCase());
                        log.info("Found exact State enum match: {}", stateEnum.name());
                        searchPredicate = cb.or(searchPredicate,
                            cb.equal(root.get("applicationState"), stateEnum.name()));
                    } catch (IllegalArgumentException ex) {
                        // Not a valid State enum either, try partial matching
                        log.info("No exact enum match, trying partial matches for enum values");
                        Predicate statusStatePredicate = cb.disjunction();
                        String searchUpper = search.toUpperCase();
                        
                        // Check for partial matches in Status enum values
                        for (Enums.Status status : Enums.Status.values()) {
                            if (status.name().contains(searchUpper) || 
                                searchUpper.contains(status.name())) {
                                log.info("Found partial Status enum match: {}", status.name());
                                statusStatePredicate = cb.or(statusStatePredicate,
                                    cb.equal(root.get("applicationStatus"), status.name()));
                            }
                        }
                        
                        // Check for partial matches in State enum values
                        for (Enums.State state : Enums.State.values()) {
                            if (state.name().contains(searchUpper) || 
                                searchUpper.contains(state.name())) {
                                log.info("Found partial State enum match: {}", state.name());
                                statusStatePredicate = cb.or(statusStatePredicate,
                                    cb.equal(root.get("applicationState"), state.name()));
                            }
                        }
                        
                        // Add the combined status/state predicate to the search predicate
                        searchPredicate = cb.or(searchPredicate, statusStatePredicate);
                    }
                }
                
                // Add company search via API
                try {
                    ApiResponse<?> companyResponse = companyClient.searchCompanyByName(search);
                    if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                        List<Map<String, Object>> companies = (List<Map<String, Object>>) companyResponse.getData();
                        if (!companies.isEmpty()) {
                            List<UUID> companyIds = companies.stream()
                                .map(company -> {
                                    try {
                                        return UUID.fromString(company.get("organizationId").toString());
                                    } catch (Exception e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!companyIds.isEmpty()) {
                                searchPredicate = cb.or(searchPredicate,
                                    root.get("organisationId").in(companyIds));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error searching companies with term '{}': {}", search, e.getMessage());
                }
                
                // Add assignee search via API
                try {
                    ApiResponse<?> userResponse = companyClient.searchUsersByUsername(search);
                    if (userResponse != null && userResponse.isStatus() && userResponse.getData() != null) {
                        List<Map<String, Object>> users = (List<Map<String, Object>>) userResponse.getData();
                        if (!users.isEmpty()) {
                            List<String> userIds = users.stream()
                                .map(user -> {
                                    try {
                                        return user.get("userId").toString();
                                    } catch (Exception e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!userIds.isEmpty()) {
                                Predicate assigneePredicate = cb.or(
                                    root.get("assignedAgent").in(userIds),
                                    root.get("assignedAgentLead").in(userIds),
                                    root.get("assignedOfficer").in(userIds),
                                    root.get("assignedOfficerLead").in(userIds),
                                    root.get("assignedManager").in(userIds)
                                );
                                searchPredicate = cb.or(searchPredicate, assigneePredicate);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error searching users with term '{}': {}", search, e.getMessage());
                }
                
                additionalPredicates.add(searchPredicate);
            }
            
            // Combine base predicate with additional predicates
            if (!additionalPredicates.isEmpty()) {
                return cb.and(
                    basePredicate,
                    cb.and(additionalPredicates.toArray(new Predicate[0]))
                );
            } else {
                return basePredicate;
            }
        };
    }
                /**
     * Builds a base specification that only includes role and company filtering
     */
    private Specification<WorkPlaceTrainingPlan> buildBaseRoleSpecification(
            String role, UUID userId, UUID organisationId) {
        
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Base condition: not deleted
            predicates.add(cb.equal(root.get("deleted"), false));
            
            // If organisationId is provided, show all data for that company
            if (organisationId != null) {
                predicates.add(cb.equal(root.get("organisationId"), organisationId));
            } else {
                // Role-based filtering
                if (role != null && userId != null) {
                    switch (role.toUpperCase()) {
                        case "AGENT":
                            // Show applications assigned to this agent (excluding drafts)
                            predicates.add(cb.and(
                                cb.equal(root.get("assignedAgent"), userId.toString()), // Convert UUID to String
                                cb.notEqual(root.get("applicationState"), "DRAFT"),
                                cb.equal(root.get("applicationState"), "IN_PROCESSING")
                            ));
                            break;
                        case "AGENT_LEAD":
                            // For AGENT_LEAD, show all applications except drafts
                            predicates.add(cb.notEqual(root.get("applicationState"), "DRAFT"));
                            break;
                        case "OFFICER":
                            // Show applications assigned to this officer (excluding drafts)
                            predicates.add(cb.and(
                                cb.equal(root.get("assignedOfficer"), userId.toString()), // Convert UUID to String
                                cb.notEqual(root.get("applicationState"), "DRAFT"),
                                cb.equal(root.get("applicationState"), "IN_REVIEW")
                            ));
                            break;
                        case "OFFICER_LEAD":
                            // For OFFICER_LEAD, show all applications except drafts
                            predicates.add(cb.and(
                                cb.notEqual(root.get("applicationState"), "DRAFT"),
                                cb.equal(root.get("applicationState"), "IN_REVIEW")
                            ));
                            break;
                        case "MANAGER":
                            // Show applications assigned to this manager (excluding drafts)
                            predicates.add(cb.and(
                                cb.notEqual(root.get("applicationState"), "DRAFT"),
                                cb.equal(root.get("applicationState"), "IN_APPROVAL")
                            ));
                            break;
                        default:
                            // For invalid roles, add a condition that will return no results
                            predicates.add(cb.equal(cb.literal(1), 0));
                    }
                }
            }

                        
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
    
    /**
     * Creates metadata for the response including pagination info and status counts.
     */
    private Map<String, Object> createMetadata(Page<WorkPlaceTrainingPlan> page, Specification<WorkPlaceTrainingPlan> baseRoleSpec) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", page.getNumber());
        metadata.put("totalPages", page.getTotalPages());
        metadata.put("totalElements", page.getTotalElements());
        metadata.put("pageSize", page.getSize());
        
        // Add status counts using our optimized method that uses a single query
        try {
            Map<String, Long> statusCounts = trainingPlanRepository.getTrainingPlanStatusCounts(baseRoleSpec);

                       
            metadata.put("statusCounts", statusCounts);
        } catch (Exception e) {
            log.error("Error calculating status counts: {}", e.getMessage());
            
        }
        
        return metadata;
    }
    
    @Override
    public Map<String, Object> createEmptyResponse(int pageNumber, int size) {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", pageNumber);
        metadata.put("totalPages", 0);
        metadata.put("totalElements", 0L);
        metadata.put("pageSize", size);

        // Add empty status counts with all the new fields
        Map<String, Object> statusCounts = new HashMap<>();
        statusCounts.put("totalApplications", 0L);
        statusCounts.put("pendingVetting", 0L);
        statusCounts.put("vettingOngoing", 0L);
        statusCounts.put("awaitingChanges", 0L);
        statusCounts.put("rejected", 0L);
        statusCounts.put("approved", 0L);
        statusCounts.put("underReview", 0L);
        statusCounts.put("pendingApproval", 0L);
        
        metadata.put("statusCounts", statusCounts);
        response.put("metadata", metadata);
        response.put("content", new ArrayList<>());
        return response;
    }
    
    /**
     * Converts a WorkPlaceTrainingPlan entity to a Map for list display
     * with flattened course details in specific order
     */
    private Map<String, Object> convertToListDto(WorkPlaceTrainingPlan plan) {
        // Use LinkedHashMap to maintain insertion order
        Map<String, Object> planMap = new LinkedHashMap<>();
        
        // Add fields in the exact order specified
       
        planMap.put("id", plan.getId());
        planMap.put("userId", plan.getUserId());
        planMap.put("organisationId", plan.getOrganisationId());
        planMap.put("applicationState", plan.getApplicationState());
        planMap.put("applicationStatus", plan.getApplicationStatus());
        planMap.put("referenceNumber", plan.getReferenceNumber());
        planMap.put("applicationNumber", plan.getApplicationNumber());
      
        
        // Company details - will be populated later if available
        planMap.put("telephoneNumber", null);
        planMap.put("companyName", null);
        
        planMap.put("submissionDate", plan.getSubmissionDate());
        planMap.put("createdDate", plan.getCreatedDate());
        
        planMap.put("assignedAgentLead", plan.getAssignedAgentLead());
        planMap.put("assignedAgent", plan.getAssignedAgent());
        planMap.put("assignedOfficerLead", plan.getAssignedOfficerLead());
        planMap.put("assignedOfficer", plan.getAssignedOfficer());
                
        planMap.put("assignedManager", plan.getAssignedManager());

     
        
        // Set assignedTo based on state and status - ensure it's always included
        if (plan.getApplicationState() != null) {
            if (plan.getApplicationState().equals("IN_APPROVAL") && 
                !plan.getApplicationStatus().equals("CHANGE_REQUEST")) {
                planMap.put("assignedTo", plan.getAssignedManager());
            } else if (plan.getApplicationState().equals("IN_REVIEW") && 
                       !plan.getApplicationStatus().equals("CHANGE_REQUEST")) {
                planMap.put("assignedTo", plan.getAssignedOfficer());
            } else if (plan.getApplicationState().equals("IN_PROCESSING") && 
                       !plan.getApplicationStatus().equals("CHANGE_REQUEST")) {
                planMap.put("assignedTo", plan.getAssignedAgent());
            } else if (plan.getApplicationState().equals("SUBMITTED") && 
                       !plan.getApplicationStatus().equals("CHANGE_REQUEST")) {
                planMap.put("assignedTo", plan.getAssignedAgentLead());
            } else {
                planMap.put("assignedTo", null);
            }
        } else {
            planMap.put("assignedTo", null);
        }
        
        
        if (plan.getCourseDetails() != null && !plan.getCourseDetails().isEmpty()) {
            CourseDetails firstCourse = plan.getCourseDetails().get(0);
            planMap.put("skills", firstCourse.getSkills());
            planMap.put("institution", firstCourse.getInstitution());
            planMap.put("levelOfTraining", firstCourse.getLevelOfTraining());
            planMap.put("costOfTraining", firstCourse.getCostOfTraining());
            planMap.put("accreditingBody", firstCourse.getAccreditingBody());
            planMap.put("programme", firstCourse.getProgramme());
        } else {
            planMap.put("skills", null);
            planMap.put("institution", null);
            planMap.put("levelOfTraining", null);
            planMap.put("costOfTraining", null);
            planMap.put("accreditingBody", null);
            planMap.put("programme", null);
        }

        planMap.put("physicalAddress", null); 

        planMap.put("email", null); 
        
        // Try to get company details if available
        try {
            if (plan.getOrganisationId() != null) {
                ApiResponse<?> companyResponse = companyClient.fetchCompanyById(plan.getOrganisationId().toString());
                if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                    Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                    planMap.put("companyName", companyData.get("name"));
                    planMap.put("physicalAddress", companyData.get("physicalAddress"));
                    planMap.put("telephoneNumber", companyData.get("telephoneNumber"));
                    planMap.put("email", companyData.get("email"));
                }
            }
        } catch (Exception e) {
            log.error("Error fetching company details for training plan {}: {}", plan.getId(), e.getMessage());
        }
        
        return planMap;
    }
    
    /**
     * Checks if a role value is valid.
     */
    private boolean isValidRole(String role) {
        try {
            Enums.UserRoles.valueOf(role.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Checks if a user ID is valid.
     */
    private boolean isValidUserId(UUID userId) {
        return userId != null;
    }


    @Override
    @Transactional
    public void softDeleteTrainingPlan(UUID id) {
        log.info("Soft deleting workplace training plan with ID: {}", id);

        WorkPlaceTrainingPlan trainingPlan = trainingPlanRepository.findById(id)
                .orElseThrow(() -> new ApplicationNotFoundException("Training Plan not found with ID: " + id));

        // Set deleted flag instead of actually deleting
        trainingPlan.setDeleted(true);
        trainingPlan.setDeletedDate(new Date());

        trainingPlanRepository.save(trainingPlan);
        log.info("Workplace training plan soft deleted with ID: {}", id);
    }

    @Override
    public Optional<WorkPlaceTrainingPlan> getTrainingPlanEntity(UUID id) {
        return trainingPlanRepository.findById(id);
    }

    @Override
    public int updateTrainingPlanAssignedUser(UUID id, Enums.UserRoles role, String userId) {
        return trainingPlanRepository.updateTrainingPlanAssignedUser(id, role.name(), userId);
    }

    @Override
    @Transactional
    public int updateTrainingPlanStatus(UUID trainingPlanId, String role, String action, String newAssignee) {
        log.info("Updating status for training plan ID: {}, role: {}, action: {}, newAssignee: {}",
            trainingPlanId, role, action, newAssignee);
            
        return trainingPlanRepository.changeTrainingPlanStatus(trainingPlanId, role, action, newAssignee);
    }

    @Override
    public String sanitizeHtml(String content) {
      if (content == null) {
            return "";
        }
        // Use Jsoup to sanitize HTML content
        return Jsoup.clean(content, Whitelist.basic());
    }

    @Override
    @Transactional
    public BatchStatusUpdateResult batchUpdateTrainingPlanStatus(
            List<UUID> applicationIds,
            String role,
            String action,
            String userId,
            String comments,
            String newAssignee) {
        
        log.info("Batch status update initiated for {} training plans", 
                applicationIds != null ? applicationIds.size() : 0);
        
        BatchStatusUpdateResult result = new BatchStatusUpdateResult();
        List<BatchStatusUpdateResult.ApplicationUpdateResult> updateResults = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // Create a new batch record
        Batch batch = new Batch();
        batch.setBatchType("training_plan");
        batch.setApplicationStatus(ApplicationStatus.valueOf(action));
        batch.setApplicationCount(applicationIds.size());
        batch.setActionTakenBy(role);
        batch.setUserId(UUID.fromString(userId));
        batch.setDatePosted(new Date());
        batch.setBatchStatus(ApplicationStatus.PROCESSING);
        
        // Save the batch to get an ID
        batch = batchRepository.save(batch);
        UUID batchId = batch.getId();
        
        for (UUID applicationId : applicationIds) {
            try {
                Optional<WorkPlaceTrainingPlan> trainingPlanOpt = getTrainingPlanEntity(applicationId);
                
                if (trainingPlanOpt.isPresent()) {
                    WorkPlaceTrainingPlan trainingPlan = trainingPlanOpt.get();
                    
                    // Check if training plan is already in REJECTED status
                    if ("REJECTED".equals(trainingPlan.getApplicationStatus())) {
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                applicationId, false, "Training plan is already rejected and cannot be updated"));
                        failureCount++;
                        continue;
                    }
                    
                    // Check if application is already in CHANGE_REQUEST status
                    if ("CHANGE_REQUEST".equals(trainingPlan.getApplicationStatus())) {
                         updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                 applicationId, false, "Application is already in change request status and cannot be approved"));
                        failureCount++;
                        continue;
                                    }
                    int updateResult = updateTrainingPlanStatus(applicationId, role, action, newAssignee);
                    
                    if (updateResult > 0) {
                        // Create and save comments if provided
                        if (comments != null && !comments.isEmpty()) {
                            WorkPlaceTrainingPlanComments logEntry = new WorkPlaceTrainingPlanComments();
                            logEntry.setTrainingPlan(trainingPlan);
                            logEntry.setAction(action);
                            logEntry.setComments(sanitizeHtml(comments));
                            logEntry.setUpdatedBy(userId);
                            logEntry.setTimestamp(LocalDateTime.now());
                            logEntry.setBatchId(batchId);
                            commentsService.createComments(logEntry);
                        }
                        
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                applicationId, true, "Status updated successfully"));
                        successCount++;
                    } else {
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                applicationId, false, "Failed to update training plan status"));
                        failureCount++;
                    }
                } else {
                    updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                            applicationId, false, "Training plan not found"));
                    failureCount++;
                }
            } catch (Exception e) {
                updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                        applicationId, false, "Error: " + e.getMessage()));
                failureCount++;
            }
        }
        
        result.setTotalProcessed(applicationIds.size());
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setResults(updateResults);
        
        // Update the batch with the results
        ApplicationStatus finalStatus = (failureCount == 0) ? 
                ApplicationStatus.PROCESSED : ApplicationStatus.PARTIALLY_PROCESSED;
        batch.setBatchStatus(finalStatus);
        
        // Store the full response in batch_logs
        Map<String, Object> batchLogs = new HashMap<>();
        batchLogs.put("timestamp", new Date());
        batchLogs.put("action", action);
        batchLogs.put("role", role);
        batchLogs.put("result", result);
        batch.setBatchLogs(batchLogs);
        
        // Update the batch record
        batchRepository.save(batch);
        
        return result;
    }

    /**
     * Fetching Course Details
     * @param id The Course Details ID
     * @return CourseDetails
     */
    @Override
    public Optional<CourseDetails> getCourseDetailsEntity(UUID id) {
        return courseDetailsRepository.findById(id);
    }

    @Override
    public void updateProcessInstanceIdToApplication(String preApprovalId, String processInstanceId) {
        WorkPlaceTrainingPlan trainingPlan = trainingPlanRepository.findById(UUID.fromString(preApprovalId))
                .orElseThrow(() -> new ApplicationNotFoundException("Training Plan not found with ID: " + preApprovalId));

        trainingPlan.setProcessInstanceId(processInstanceId);
        trainingPlanRepository.save(trainingPlan);
        log.info("Updated process instance ID for training plan {} to {}", preApprovalId, processInstanceId);
    }
}
