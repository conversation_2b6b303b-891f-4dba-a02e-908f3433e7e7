package bw.org.hrdc.weblogic.workplacelearning.service.common;

import bw.org.hrdc.weblogic.workplacelearning.entity.common.Quotation;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.repository.common.QuoteRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @CreatedOn 24/05/25 07:30
 * @UpdatedBy martinspectre
 * @UpdatedOn 24/05/25 07:30
 */
@Service
public class QuotationService {
    @Autowired
    private QuoteRepository quotationRepository;

    public Map<String, Object> calculateNocQuotation(NCBSCApplication original, NOCApplication changes) {
        Map<String, String> changeMessages = new LinkedHashMap<>();
        Map<String, Object> response = new HashMap<>();
        double cost = 0.00;
        boolean hasMajorChange = !Objects.equals(original.getShortCourseInformation().getTitle(), changes.getShortCourseInformation().getTitle());
        if(hasMajorChange){
            changeMessages.put("title", "Title has been changed");
        }
        // Check major fields
        if (!Objects.equals(original.getShortCourseInformation().getFieldOfLearning(), changes.getShortCourseInformation().getFieldOfLearning())){
            hasMajorChange = true;
            changeMessages.put("fieldOfLearning", "Field of Learning has been changed");
        }
        if (!Objects.equals(original.getShortCourseInformation().getSubFieldOfLearning(), changes.getShortCourseInformation().getSubFieldOfLearning())){
            hasMajorChange = true;
            changeMessages.put("subFieldOfLearning", "Subfield of Learning has been changed");
        }
        if (!Objects.equals(original.getShortCourseInformation().getAccreditingBody(), changes.getShortCourseInformation().getAccreditingBody())){
            hasMajorChange = true;
            changeMessages.put("entryRequirements", "Entry Requirements have been changed");
        }

        if (hasMajorChange) {
            cost =  2500;
        } else {
            changeMessages.put("common", "Requested minor changes");
        }
        response.put("hasMajorChange", hasMajorChange);
        response.put("cost", cost);
        response.put("changes", changeMessages);
        return response;
    }

    public Page<Quotation> findAll(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return quotationRepository.findAll(pageable);
    }

    public Optional<Quotation> getByReference(String referenceNumber) {
        return quotationRepository.findByQuoteReference(referenceNumber);
    }

    public Optional<Quotation> getQuoteByReference(String referenceNumber) {
        return quotationRepository.findByQuoteRef(referenceNumber);
    }

    public Optional<Quotation> findById(String id) {
        return quotationRepository.findByUuid(id);
    }

    public Quotation save(NCBSCApplication original, NOCApplication changes) {
        Map<String, Object> quotationObject = calculateNocQuotation(original, changes);
        Quotation quotation = new Quotation();
        quotation.setReference(changes.getUuid());
        quotation.setChecklistItemsJson(String.valueOf(quotationObject.get("changes")));
        quotation.setAccepted(false);
        String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("quo");
        quotation.setQuoteRef(referenceNumber);
        double quoteCost = (double) quotationObject.get("cost");
        quotation.setTotalAmount(quoteCost);
        return quotationRepository.save(quotation);
    }

    public void deleteById(String id) {
        quotationRepository.deleteQuote(id);
    }

    public Quotation acceptQuote(String quoteId, boolean isAccepted) {
        return findById(quoteId)
                .map(quotation -> {
                    quotation.setAccepted(isAccepted);
                    return quotationRepository.save(quotation);
                })
                .orElseThrow(() -> new RuntimeException("Quotation not found with ID: " + quoteId));
    }
}
