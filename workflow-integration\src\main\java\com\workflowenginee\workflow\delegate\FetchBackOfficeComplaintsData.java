package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

@Component("fetchBackOfficeComplaintsData")
public class FetchBackOfficeComplaintsData implements JavaDelegate{

    private final WorkplaceLearningClient workplaceLearningClient;

    public FetchBackOfficeComplaintsData(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");
        String applicationType = (String) execution.getVariable("applicationType");

        System.out.println("[Process: " + processInstanceId + "] Fetching back office complaint data for ID: " + complaintId);

        ApiResponse<?> response = null;
        try {
            response = workplaceLearningClient.getComplaintById(complaintId);

            System.out.println("Back office complaint response: " + response);
            
            if (response != null && response.getData() instanceof Map) {
                Map<String, Object> responseData = (Map<String, Object>) response.getData();
                execution.setVariable("applicationData", responseData);
                
                // Extract complaint status for workflow decisions
                String complaintStatus = (String) responseData.get("status");
                String complaintState = (String) responseData.get("state");
                String companyId = (String) responseData.get("organisationId");
                String referenceNumber = (String) responseData.get("referenceNumber");
                String finalStatus = null;

                if (Enums.ComplaintState.COMPLETED.name().equalsIgnoreCase(complaintState)){
                    finalStatus = "completed";
                } else if (Enums.ComplaintState.ESCALATED.name().equalsIgnoreCase(complaintState)) {
                    finalStatus = "escalated";
                }
                
                execution.setVariable("complaintStatus", complaintStatus);
                execution.setVariable("status", finalStatus);
                execution.setVariable("complaintState", complaintState);
                execution.setVariable("applicationType", applicationType);
                execution.setVariable("companyId", companyId);
                execution.setVariable("referenceNumber", referenceNumber);
                
                System.out.println("[Process: " + processInstanceId + "] Set complaint data variables. Status: " + complaintStatus + ", State: " + complaintState);
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty complaint response data.");
                execution.setVariable("fetchError", "Invalid or empty complaint response data");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error fetching complaint data: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("fetchError", "Error during complaint data fetch: " + e.getMessage());
        }
    }

}
