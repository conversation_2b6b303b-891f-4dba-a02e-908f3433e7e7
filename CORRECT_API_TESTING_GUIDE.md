# Correct API Testing Guide for Complaint Workflow

## ❌ **Error Analysis**

The error you received indicates that the `resume-complaint-lifecycle` endpoint requires a **request body**, but you sent the request without one.

**Error Message:**
```
Required request body is missing: public org.springframework.http.ResponseEntity<?> com.workflowenginee.workflow.controller.WorkflowController.resumeComplaintLifecycle(java.lang.String,java.lang.String,java.util.Map<java.lang.String, java.lang.Object>)
```

## ✅ **Correct Request Format**

### **Required Request Body Fields:**
- `complaintId` (required) - The complaint identifier
- `role` (optional) - The role of the user (Agent, AgentLead, etc.)

## 🧪 **Complete Testing Procedure**

### **Step 1: Start Complaint Workflow**
```bash
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/cc610b03-df26-410b-8623-236e47afdc6c
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle process started successfully",
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "complaintData": {
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "status": "OPEN"
  }
}
```

### **Step 2: Check Initial Status**
```bash
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/b59ad3f8-4b5d-11f0-be36-14857f938c25
```

**Expected Response:**
```json
{
  "success": true,
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "activeActivities": ["signalNotifyAgent"],
  "currentStatus": "WAITING_FOR_AGENT_DECISION",
  "nextAction": "Send NotifyAgent signal to trigger automatic decision",
  "variables": {
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "agentNotified": true
  }
}
```

### **Step 3: Send NotifyAgent Signal (CORRECTED)**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/b59ad3f8-4b5d-11f0-be36-14857f938c25/NotifyAgent
Content-Type: application/json

{
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
  "role": "Agent"
}
```

**Expected Response:**
```json
{
  "success": true,
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "signalType": "NotifyAgent",
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
  "message": "NotifyAgent signal sent successfully - automatic decision will be made",
  "automaticDecision": true,
  "nextStep": "System will automatically analyze complaint data and decide to close or escalate",
  "decisionFactors": ["priority", "typeOfComplaint", "department"],
  "possibleOutcomes": ["close", "escalate"]
}
```

### **Step 4: Check Status After Signal**
```bash
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/b59ad3f8-4b5d-11f0-be36-14857f938c25
```

**Expected Response (if closed):**
```json
{
  "success": true,
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "activeActivities": [],
  "currentStatus": "COMPLETED",
  "isEnded": true,
  "variables": {
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "agentDecision": "close",
    "complaintStatus": "CLOSED",
    "closedBy": "AGENT",
    "clientNotified": true
  }
}
```

**Expected Response (if escalated):**
```json
{
  "success": true,
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "activeActivities": ["signalEscalated"],
  "currentStatus": "WAITING_FOR_ESCALATION_COMPLETION",
  "nextAction": "Send Escalated signal to complete escalation",
  "variables": {
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "agentDecision": "escalate"
  }
}
```

### **Step 5: If Escalated, Send Escalated Signal**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/b59ad3f8-4b5d-11f0-be36-14857f938c25/Escalated
Content-Type: application/json

{
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
  "role": "AgentLead"
}
```

**Expected Response:**
```json
{
  "success": true,
  "processInstanceId": "b59ad3f8-4b5d-11f0-be36-14857f938c25",
  "signalType": "Escalated",
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
  "message": "Escalated signal sent successfully - escalation will be completed automatically",
  "automaticCompletion": true,
  "nextStep": "Agent lead will automatically close the complaint and notify the client",
  "expectedOutcome": "Process completion with client notification"
}
```

## 📋 **Request Body Requirements**

### **For NotifyAgent Signal:**
```json
{
  "complaintId": "string (required)",
  "role": "string (optional, default: Agent)"
}
```

### **For Escalated Signal:**
```json
{
  "complaintId": "string (required)",
  "role": "string (optional, default: AgentLead)"
}
```

## 🔍 **Troubleshooting Common Issues**

### **1. Missing Request Body (400 Error)**
**Problem:** Sending request without JSON body
**Solution:** Always include Content-Type header and JSON body

**Incorrect:**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{id}/NotifyAgent
```

**Correct:**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{id}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "your-complaint-id"
}
```

### **2. Missing complaintId (400 Error)**
**Problem:** Request body without complaintId field
**Solution:** Include complaintId in request body

**Incorrect:**
```json
{
  "role": "Agent"
}
```

**Correct:**
```json
{
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
  "role": "Agent"
}
```

### **3. Process Not Found (404 Error)**
**Problem:** Invalid process instance ID
**Solution:** Use correct process instance ID from start response

### **4. Process Already Completed (409 Error)**
**Problem:** Trying to signal a completed process
**Solution:** Check process status first

## 🎯 **Expected Kafka Messages**

### **After NotifyAgent Signal:**
```json
{
  "subject": "New Complaint Assignment",
  "recipient": "<EMAIL>",
  "message": "A new complaint has been assigned to you",
  "type": "IN_APP",
  "application_type": "COMPLAINTS"
}
```

### **After Process Completion:**
```json
{
  "subject": "Your Complaint Has Been Resolved",
  "recipient": "<EMAIL>",
  "message": "Dear Valued Client, your complaint has been resolved...",
  "type": "EMAIL",
  "application_type": "COMPLAINTS"
}
```

## ✅ **Success Indicators**

1. **Process Starts:** Returns process instance ID
2. **Signal Accepted:** Returns success message with next steps
3. **Automatic Decisions:** Process variables show decision made
4. **Kafka Messages:** Check communication service logs
5. **Process Completion:** Status shows COMPLETED
6. **Client Notification:** clientNotified = true in variables

## 🚀 **Quick Test Command**

Use this corrected command for your specific process:

```bash
curl -X POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/b59ad3f8-4b5d-11f0-be36-14857f938c25/NotifyAgent \
  -H "Content-Type: application/json" \
  -d '{
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "role": "Agent"
  }'
```

This should resolve the "Required request body is missing" error and allow the workflow to proceed correctly.
