server:
  port: 3450

spring:
  application:
    name: otp-service
  profiles:
    active: ${PROFILE_ACTIVE:local}
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:ehrdcf_uaa}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:administrator}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  # Kafka
  kafka:
    consumer:
      group-id: otp-service-group
    bootstrap-servers: ${KAFKA_SERVER:localhost:9094}

otp:
  service:
    url: http://localhost:${server.port}/
  consumer:
    group-id: otp-group
  topic:
    name: otp-topic

notification:
  topic:
      name: sms-email-notification
# Logging Configuration
logging:
  level:
    org.springframework.security: INFO

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}
    healthcheck:
      enabled: true