package bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.util.BigDecimalCommaDeserializer;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "course_details")
@Getter
@Setter
@ToString
public class CourseDetails {

    @Id
    @GeneratedValue
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private WorkPlaceTrainingPlan application;

    @Column(nullable = false)
    private String programme;

    @Column(nullable = false)
    private String skills;

    @Column(name = "training_start_date", nullable = false)
    private LocalDate trainingStartDate;

    @Column(name = "training_end_date", nullable = false)
    private LocalDate trainingEndDate;

    @Column(nullable = false)
    private String institution;

    @Column(nullable = false)
    private String location;

    @Column(name = "accrediting_body", nullable = false)
    private String accreditingBody;

    @Column(name = "level_of_training", nullable = false)
    private String levelOfTraining;

    @JsonDeserialize(using = BigDecimalCommaDeserializer.class)
    @Column(name = "cost_of_training", nullable = false)
    private BigDecimal costOfTraining;

    @Column(name = "noncitizens", nullable = false)
    private Integer noncitizens;

    @Column(name = "citizens", nullable = false)
    private Integer citizens;

    @Column(name = "people_trained", nullable = false)
    private Integer peopleTrained;

    @Column( name = "course_id", nullable = true)
    private String courseId;

    @Column( name = "is_not_local_course", nullable = true)
    private Boolean isNotLocalCourse;

    @Column( name = "has_pre_approval", nullable = true)
    private Boolean hasPreApproval = false;
}
