package com.workflowenginee.workflow.delegate;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("notifyCompltedComplaints")
public class NotifyCompltedComplaints implements JavaDelegate{

    @Autowired
    private NotificationComplaintService notificationComplaintService;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");
        String complaintStatus = (String) execution.getVariable("complaintStatus");
        String complaintState = (String) execution.getVariable("complaintState");
        String applicationType = Enums.ApplicationType.COMPLAINTS.name();
        String referenceNumber = (String) execution.getVariable("referenceNumber");
        String companyId = (String) execution.getVariable("companyId");

        try {
             NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                .referenceNumber(referenceNumber)
                .applicationId(complaintId)
                .applicationStatus(complaintStatus)
                .companyId(companyId)
                .applicationState(complaintState)
                .applicationType(applicationType)
                .build();
                notificationComplaintService.notificationToClient(notifyclientDto);
        } catch (Exception e) {
            // TODO: handle exception
            System.out.println("[Process: " + processInstanceId + "] Error sending notification to client: " + e.getMessage());
        }
    }

}
