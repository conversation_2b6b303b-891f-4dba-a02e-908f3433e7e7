package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;
import java.util.UUID;

/**
 * Specification for WorkPlaceTrainingPlan entity.
 */
public class WorkPlaceTrainingPlanSpecification {

    /**
     * Creates a specification for filtering WorkPlaceTrainingPlans by various criteria.
     *
     * @param userId           the ID of the user who created the training plan.
     * @param organisationId   the ID of the organisation submitting the training plan.
     * @param applicationStatus the status of the training plan application.
     * @return a specification for filtering WorkPlaceTrainingPlans.
     */
    public static Specification<WorkPlaceTrainingPlan> searchByCriteria(
            Date submissionDate, UUID userId, UUID organisationId, String applicationStatus, String referenceNumber) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            if (userId != null) {
                p = cb.and(p, cb.equal(root.get("userId"), userId));
            }
            if (organisationId != null) {
                p = cb.and(p, cb.equal(root.get("organisationId"), organisationId));
            }
            if (applicationStatus != null && !applicationStatus.isEmpty()) {
                p = cb.and(p, cb.equal(root.get("applicationStatus"), applicationStatus));
            }
            if (referenceNumber != null && !referenceNumber.isEmpty()) {
                // Use partial matching with case insensitive comparison
                String refSearchTerm = "%" + referenceNumber.toLowerCase() + "%";
                p = cb.and(p, cb.like(cb.lower(root.get("referenceNumber")), refSearchTerm));
            }

            return p;
        };
    }
}
