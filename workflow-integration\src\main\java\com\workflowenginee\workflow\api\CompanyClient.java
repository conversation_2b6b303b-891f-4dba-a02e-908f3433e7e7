package com.workflowenginee.workflow.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.workflowenginee.workflow.config.FeignConfig;
import com.workflowenginee.workflow.util.ApiResponse;

@FeignClient(name = "ACCOUNT-SERVICE", fallback = CompanyClientFallback.class, configuration = FeignConfig.class)
public interface CompanyClient {

    @GetMapping("/api/v1/user/backoffice/users-by-role/{role}")
    ApiResponse<?> fetchAllActiveUsers(@PathVariable("role") String role);

    @GetMapping("/api/v1/company/{id}")
    ApiResponse<?> fetchCompanyById(@PathVariable String id);
    @GetMapping("/api/v1/user/backoffice/user/{userId}")
    ApiResponse<?> fetchUserById(@PathVariable("userId") String userId);

}
