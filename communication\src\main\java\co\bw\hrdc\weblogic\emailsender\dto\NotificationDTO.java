package co.bw.hrdc.weblogic.emailsender.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class NotificationDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("subject")
    private String subject;

    @JsonProperty("recipient")
    private String recipient;

    @JsonProperty("message")
    private String message;

    @JsonProperty("sent_at")
    private String sentAt;

    @JsonProperty("type")
    private String type;

    @JsonProperty("status")
    private String status;

    @JsonProperty("application_type")
    private String applicationType;

    @JsonProperty("application_id")
    private String applicationId;

    // Default constructor
    public NotificationDTO() {}

    // Constructor with all fields
    public NotificationDTO(String id, String name, String subject, String recipient, String message,
                          String sentAt, String type, String status, String applicationType, String applicationId) {
        this.id = id;
        this.name = name;
        this.subject = subject;
        this.recipient = recipient;
        this.message = message;
        this.sentAt = sentAt;
        this.type = type;
        this.status = status;
        this.applicationType = applicationType;
        this.applicationId = applicationId;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getSubject() { return subject; }
    public void setSubject(String subject) { this.subject = subject; }

    public String getRecipient() { return recipient; }
    public void setRecipient(String recipient) { this.recipient = recipient; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public String getSentAt() { return sentAt; }
    public void setSentAt(String sentAt) { this.sentAt = sentAt; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getApplicationType() { return applicationType; }
    public void setApplicationType(String applicationType) { this.applicationType = applicationType; }

    public String getApplicationId() { return applicationId; }
    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }
}
