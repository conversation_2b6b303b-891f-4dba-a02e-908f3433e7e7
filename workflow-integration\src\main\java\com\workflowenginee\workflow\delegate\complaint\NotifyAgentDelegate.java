package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("notifyAgentDelegate")
public class NotifyAgentDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String assignee = (String) execution.getVariable("assignee");
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            
            log.info("Notifying agent {} for complaint ID: {}", assignee, complaintId);

            // Prepare notification payload
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("assignee", assignee);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("action", "ASSIGNED");
            notificationPayload.put("message", "A new complaint has been assigned to you");
            notificationPayload.put("type", "COMPLAINT_ASSIGNMENT");

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            // Send notification (this could be email, SMS, or in-app notification)
            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            
            try {
                ResponseEntity<Map> response = restTemplate.exchange(
                    notificationUrl, 
                    HttpMethod.POST, 
                    entity, 
                    Map.class
                );
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    log.info("Successfully notified agent {} for complaint {}", assignee, complaintId);
                    execution.setVariable("notificationSent", true);
                } else {
                    log.warn("Failed to send notification to agent {} for complaint {}", assignee, complaintId);
                    execution.setVariable("notificationSent", false);
                }
            } catch (Exception e) {
                log.warn("Notification service unavailable, continuing workflow: {}", e.getMessage());
                execution.setVariable("notificationSent", false);
            }

            // Update complaint status to indicate agent has been notified
            execution.setVariable("agentNotified", true);
            execution.setVariable("lastAction", "AGENT_NOTIFIED");
            
        } catch (Exception e) {
            log.error("Error notifying agent: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to notify agent: " + e.getMessage());
            execution.setVariable("notificationSent", false);
            // Don't throw exception as notification failure shouldn't stop the workflow
        }
    }
}
