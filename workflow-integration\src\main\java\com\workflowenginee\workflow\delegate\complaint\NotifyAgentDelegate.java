package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.service.NotificationService;
import com.workflowenginee.workflow.util.Enums;

import lombok.extern.slf4j.Slf4j;

@Component("notifyAgentDelegate")
@Slf4j
public class NotifyAgentDelegate implements JavaDelegate {

    @Autowired
    private NotificationService notificationService;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");

        log.info("[Process: {}] Notifying agent for complaint: {}", processInstanceId, complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");

            if (complaintData != null) {
                String assignee = (String) complaintData.get("assignee");
                String referenceNumber = (String) complaintData.get("referenceNumber");
                String organisationId = (String) complaintData.get("organisationId");
                String status = (String) complaintData.get("status");

                // Use assignee if available, otherwise use default agent
                String agentId = assignee != null ? assignee : "SYSTEM_AGENT";

                log.info("[Process: {}] Sending Kafka notification to agent: {} for complaint: {}",
                    processInstanceId, agentId, referenceNumber);

                // Send notification via Kafka to specific agent
                String subject = "New Complaint Assignment";
                String message = "A new complaint " + (referenceNumber != null ? referenceNumber : complaintId) +
                               " has been assigned to you for review";

                notificationService.notifySpecificUser(
                    agentId,                                    // userId (agent ID)
                    referenceNumber != null ? referenceNumber : complaintId,  // referenceNumber
                    complaintId,                                // applicationId
                    subject,                                    // subject
                    message,                                    // message
                    Enums.ApplicationType.COMPLAINTS.name()    // applicationType
                );

                // Also send notification to agent leads if this is an escalation
                String currentActivity = execution.getCurrentActivityId();
                if ("notifyEscalation".equals(currentActivity)) {
                    log.info("[Process: {}] Sending escalation notification to agent leads", processInstanceId);

                    // Create DTO for role-based notification using builder pattern
                    com.workflowenginee.workflow.dto.NotifyUsersByRoleDto roleNotificationDto =
                        com.workflowenginee.workflow.dto.NotifyUsersByRoleDto.builder()
                            .role("AGENT_LEAD")
                            .referenceNumber(referenceNumber != null ? referenceNumber : complaintId)
                            .applicationId(complaintId)
                            .applicationStatus(status != null ? status : "ESCALATED")
                            .applicationType(Enums.ApplicationType.COMPLAINTS.name())
                            .build();

                    notificationService.notifyUsersByRole(roleNotificationDto);

                    execution.setVariable("agentLeadsNotified", true);
                }

                execution.setVariable("agentNotified", true);
                execution.setVariable("notifiedAgent", agentId);

                log.info("[Process: {}] Kafka notification sent successfully to agent: {}", processInstanceId, agentId);

            } else {
                log.warn("[Process: {}] No complaint data available for notification", processInstanceId);
                execution.setVariable("agentNotified", false);
            }

        } catch (Exception e) {
            log.error("[Process: {}] Error sending agent notification: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("agentNotified", false);
            execution.setVariable("notificationError", e.getMessage());
        }
    }
}
