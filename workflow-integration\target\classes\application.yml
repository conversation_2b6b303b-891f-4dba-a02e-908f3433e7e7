server:
  port: 8082

spring:
  application:
    name: workflow
  # profiles:
  #   active: local
  kafka:
    bootstrap-servers: ${KAFKA_SERVER:localhost:9094}
    template:
      default-topic: notifications
  jackson:
    serialization:
      INDENT_OUTPUT: true
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DB:workflow}
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
  data:
    jpa:
      repositories:
        enabled: true


  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB

jwt:
  auth:
    converter:
      resource-id: ${spring.security.oauth2.client.registration.keycloak.client-id}
      principal-attribute: principal_username

keycloak:
  server-url: ${KC_HOST_URI:http://localhost:32770}
  realm: ${KC_REALM:hrdc}
  client-id: ${KC_CLIENT_ID:ehrdf}
  client-secret: ${KC_CLIENT_SECRET:1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd}
  admin-username: ${KC_USERNAME:admin}
  admin-password: ${KC_PASSWORD:admin}
  protocol: ${KC_PROTOCOL:http}

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: false  # Disable for standalone testing
    registerWithEureka: false  # Disable for standalone testing
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}
    healthcheck:
      enabled: true

feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
  circuitbreaker:
    enabled: true

# Service URLs
workplace:
  learning:
    service:
      url: ${WORKPLACE_LEARNING_SERVICE_URL:http://localhost:8091}

logging:
  level:
    org.springframework:
      security: INFO
      web: INFO
    org:
      hibernate:
        SQL=DEBUG: DEBUG


management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

app:
  logging:
    level: "ERROR"

error:
  whitelabel:
    enabled: true


# Flowable Configuration
flowable:
  database-schema-update: true
  async-executor-activate: true
  database-schema: public
  