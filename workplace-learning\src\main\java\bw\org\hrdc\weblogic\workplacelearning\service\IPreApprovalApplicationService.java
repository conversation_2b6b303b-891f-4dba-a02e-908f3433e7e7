package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.dto.BatchStatusUpdateResult;
import bw.org.hrdc.weblogic.workplacelearning.dto.PreApprovalApplicationDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.PreApprovalApplicationListDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.PreApprovalApplicationResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.PreApprovalApplicationFilter;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;
import java.util.Optional;
import java.util.List;
import java.util.Map;

/**
 * Service interface for PreApprovalApplication operations.
 */
public interface IPreApprovalApplicationService {

    /**
     * Creates a new Pre-Approval Application.
     *
     * @param applicationDto the application data
     * @return the ID of the created application
     */
    PreApprovalApplicationResponseDto createPreApprovalApplication(PreApprovalApplicationDto applicationDto);

   
    /**
     * Updates an existing Pre-Approval Application.
     *
     * @param applicationDto the application data
     */
    void updatePreApprovalApplication(PreApprovalApplicationDto applicationDto);

    /**
     * Soft deletes a Pre-Approval Application.
     *
     * @param id the application ID
     */
    void softDeletePreApprovalApplication(UUID id);

    /**
     * Updates the status of multiple pre-approval applications in a batch
     * @param applicationReferences The list of application references to update
     * @param role The role of the user making the update
     * @param action The action being performed
     * @param userId The ID of the user making the update
     * @param comments Any comments associated with the update
     * @param newAssignee The new assignee (if any)
     * @return The result of the batch update
     */
    BatchStatusUpdateResult batchUpdateApplicationStatus(
            List<UUID> applicationId,
            String role,
            String action,
            String userId,
            String comments,
            String newAssignee);

    /**
     * Get Pre-Approval Application entity by ID
     * @param id The application ID
     * @return Optional containing the application entity if found
     */
    Optional<PreApprovalApplication> getPreApprovalApplicationEntity(UUID id);

    /**
     * Update the assigned user for a Pre-Approval Application
     * @param id The application ID
     * @param role The role of the user being assigned
     * @param userId The ID of the user being assigned
     * @return Number of records updated
     */
    int updateApplicationAssignedUser(UUID id, Enums.UserRoles role, String userId);

    /**
     * Updates the status of a pre-approval application
     * @param applicationId The ID of the application to update
     * @param role The role of the user making the update
     * @param action The action being performed
     * @param newAssignee The new assignee (if any)
     * @return The number of records updated (1 for success, 0 for failure)
     */
    int updateApplicationStatus(UUID applicationId, String role, String action, String newAssignee);

    /**
     * Sanitizes HTML content to prevent XSS attacks
     * @param content The content to sanitize
     * @return The sanitized content
     */
    String sanitizeHtml(String content);

    /**
     * Fetches all Pre-Approval Applications for a specific company.
     *
     * @param companyId the company ID
     * @param pageable the pagination information
     * @return a page of Pre-Approval Applications
     */
    Page<PreApprovalApplicationListDto> getAllCompanyApplications(UUID companyId, Pageable pageable);

    /**
     * Retrieves a Pre-Approval Application by its application number.
     *
     * @param applicationNumber the application number
     * @return Optional containing the application if found
     */
    PreApprovalApplicationDto getApplicationByApplicationNumber(String applicationNumber);

    /**
     * Get Pre-Approval Application entity by VAT number
     * @param vatNumber The VAT number to search for
     * @return Optional containing the application entity if found
     */
    Optional<PreApprovalApplication> getPreApprovalApplicationEntityByVatNumber(String vatNumber);

    /**
     * Finds all Pre-Approval Applications matching the given specification and pageable.
     *
     * @param spec the specification to filter by
     * @param pageable the pagination information
     * @return a page of matching applications
     */
    Page<PreApprovalApplication> findAll(Specification<PreApprovalApplication> spec, Pageable pageable);

    /**
     * Counts the number of Pre-Approval Applications matching the given specification.
     *
     * @param spec the specification to filter by
     * @return the count of matching applications
     */
    long count(Specification<PreApprovalApplication> spec);

    /**
     * Fetches applications based on role and various filter criteria
     * 
     * @param role the user role
     * @param userId the user ID
     * @param companyId the company ID
     * @param applicationStatus the application status
     * @param applicationState the application state
     * @param trainingProvider the training provider
     * @param startDate the start date for filtering
     * @param endDate the end date for filtering
     * @param vatNumber the VAT number
     * @param companyName the company name
     * @param courseTitle the course title
     * @param assignee the assignee username
     * @param search the unified search term
     * @param pageNumber the page number
     * @param size the page size
     * @return Map containing metadata and content
     */
    Map<String, Object> getApplicationsByRole(
            String role, String userId, UUID companyId, 
            String applicationStatus, String applicationState, String trainingProvider,
            Date startDate, Date endDate, String vatNumber, String referenceNumber,
            String companyName, String courseTitle, String assignee, 
            String search, int pageNumber, int size);

    /**
     * Creates an empty response with default metadata
     * 
     * @param pageNumber the page number
     * @param size the page size
     * @return Map containing empty metadata and content
     */
    Map<String, Object> createEmptyResponse(int pageNumber, int size);

	boolean updateProcessInstanceIdToPreApprovalApplication(String preApprovalId, String processInstanceId);
}
