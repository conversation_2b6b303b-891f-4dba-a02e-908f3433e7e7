package com.workflowenginee.workflow.delegate;

import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;

@Component("complaintsEscalatedData")
public class ComplaintsEscalatedData implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;
    public ComplaintsEscalatedData(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }

	@Override
	public void execute(DelegateExecution execution) {

         String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");
        String complaintStatus = (String) execution.getVariable("complaintStatus");
        String complaintState = (String) execution.getVariable("complaintState");
        String applicationType = Enums.ApplicationType.COMPLAINTS.name();
        String referenceNumber = (String) execution.getVariable("referenceNumber");
        String companyId = (String) execution.getVariable("companyId");
        String assignedRole = null;

        System.out.println(" role : " + role);

        if(Enums.Role.AGENT.name().equalsIgnoreCase(role)){
            assignedRole = Enums.Role.OFFICER_LEAD.name();
        } else if (Enums.Role.OFFICER.name().equalsIgnoreCase(role)) {
            assignedRole = Enums.Role.MANAGER.name();
        }
        System.out.println("assignedRole : " + assignedRole);

        try {
             NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(assignedRole)
                .referenceNumber(referenceNumber)
                .applicationId(complaintId)
                .applicationStatus(complaintStatus)
                .applicationType(applicationType)
                .build();
                
            notificationComplaintService.notificationToLeads(notifyUsersByRoleDto);
        } catch (Exception e) {
            // TODO: handle exception
            System.out.println(e.getMessage());
        }
	}

}
