<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef"
             exporter="Flowable Open Source Modeler"
             exporterVersion="6.8.1">

  <!-- Signal declarations -->
  <signal id="ProcessSignal_AgentAction" name="ProcessSignal_AgentAction"/>
  <signal id="ProcessSignal_OfficerAction" name="ProcessSignal_OfficerAction"/>
  <signal id="ProcessSignal_ManagerAction" name="ProcessSignal_ManagerAction"/>

  <process id="workflow_complaints" name="Ncbsc workflow" isExecutable="true">

    <startEvent id="startEvent" name="Start" flowable:formFieldValidation="true"/>
    <serviceTask id="serviceTask_FetchNCBSC" name="Fetch NCBSC" flowable:delegateExpression="${fetchComplaintDataDelegate}"/>
    <sequenceFlow id="flow_StartToFetchNCBSC" sourceRef="startEvent" targetRef="serviceTask_FetchNCBSC"/>

    <serviceTask id="serviceTask_LeadNotify" name="Agent Lead Notify" flowable:delegateExpression="${notifyToLeads}"/>
    <sequenceFlow id="flow_FetchNCBSCToLeadNotify" sourceRef="serviceTask_FetchNCBSC" targetRef="serviceTask_LeadNotify"/>

    <intermediateCatchEvent id="AgentAction" name="AgentAction">
      <signalEventDefinition signalRef="ProcessSignal_AgentAction"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow_LeadNotifyToAgentAction" sourceRef="serviceTask_LeadNotify" targetRef="AgentAction"/>

    <serviceTask id="serviceTask_FetchBackofficeData" name="Fetch Application(Agent Review)" flowable:delegateExpression="${fetchBackOfficeComplaintsData}"/>
    <sequenceFlow id="flow_AgentActionToFetchBackofficeData" sourceRef="AgentAction" targetRef="serviceTask_FetchBackofficeData"/>
    <sequenceFlow id="flow_FetchBackofficeDataToCheckStatusApproved" sourceRef="serviceTask_FetchBackofficeData" targetRef="checkStatusApproved"/>

    <exclusiveGateway id="checkStatusApproved" name="CheckStatus" default="flow_to_checkRejectionOrInfo"/>
    <sequenceFlow id="flow_CheckStatusApprovedToNextStep" name="Approved" sourceRef="checkStatusApproved" targetRef="serviceTask_NotifyOfficerLead">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'completed'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_to_checkRejectionOrInfo" sourceRef="checkStatusApproved" targetRef="checkRejectionOrInfo"/>

    <serviceTask id="serviceTask_NotifyOfficerLead" name="Notify Officer Lead and Client" flowable:delegateExpression="${notifyCompltedComplaints}"/>

    <intermediateCatchEvent id="OfficerAction" name="OfficerAction">
      <signalEventDefinition signalRef="ProcessSignal_OfficerAction"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow_NotifyOfficerLeadToOfficerAction" sourceRef="serviceTask_NotifyOfficerLead" targetRef="OfficerAction"/>

    <!-- Agent rejection/info -->
    <exclusiveGateway id="checkRejectionOrInfo" name="Check Rejection or Info Request"/>
    <sequenceFlow id="flow_rejected" sourceRef="checkRejectionOrInfo" targetRef="sendRejectionNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'escalated'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_info_request" sourceRef="checkRejectionOrInfo" targetRef="sendInfoRequestNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'info-request'}]]></conditionExpression>
    </sequenceFlow>

    <serviceTask id="sendRejectionNotification" name="Send Rejection Notification to client" flowable:delegateExpression="${complaintsEscalatedData}"/>
    <serviceTask id="sendInfoRequestNotification" name="Send Info Request Notification to client" flowable:delegateExpression="${infoRequestNotificationDelegate}"/>
    <sequenceFlow id="flow_AgentInfoRequestToAgentAction" sourceRef="sendInfoRequestNotification" targetRef="AgentAction"/>

    <!-- Officer review -->
    <serviceTask id="serviceTask_FetchApplicationAfterOfficer" name="Fetch Application (Officer Review)" flowable:delegateExpression="${fetchBackOfficeDataDelegate}"/>
    <sequenceFlow id="flow_OfficerActionToFetchApplication" sourceRef="OfficerAction" targetRef="serviceTask_FetchApplicationAfterOfficer"/>
    <sequenceFlow id="flow_officerBackofficeDataToCheckStatusApproved" sourceRef="serviceTask_FetchApplicationAfterOfficer" targetRef="checkStatusAfterOfficer"/>

    <exclusiveGateway id="checkStatusAfterOfficer" name="Officer Check Status" default="flow_OfficerToCheckRejectionOrInfo"/>
    <sequenceFlow id="flow_OfficerApprovedToManagerSignal" name="Approved" sourceRef="checkStatusAfterOfficer" targetRef="serviceTask_Notifymanager">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'completed'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_OfficerToCheckRejectionOrInfo" sourceRef="checkStatusAfterOfficer" targetRef="checkRejectionOrInfoAfterOfficer"/>

    <exclusiveGateway id="checkRejectionOrInfoAfterOfficer" name="Officer Check Rejection or Info Request"/>
    <sequenceFlow id="flow_officer_rejected" sourceRef="checkRejectionOrInfoAfterOfficer" targetRef="officersendRejectionNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'escalated'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_officer_info_request" sourceRef="checkRejectionOrInfoAfterOfficer" targetRef="officersendInfoRequestNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'info-request'}]]></conditionExpression>
    </sequenceFlow>

    <serviceTask id="officersendRejectionNotification" name="Send Rejection Notification to client" flowable:delegateExpression="${complaintsEscalatedData}"/>
    <serviceTask id="officersendInfoRequestNotification" name="Send Info Request Notification to client" flowable:delegateExpression="${infoRequestNotificationDelegate}"/>
    <sequenceFlow id="flow_OfficerInfoRequestToOfficerAction" sourceRef="officersendInfoRequestNotification" targetRef="OfficerAction"/>

    <serviceTask id="serviceTask_Notifymanager" name="Notify manager and Client" flowable:delegateExpression="${complaintsEscalatedData}"/>
    <intermediateCatchEvent id="ManagerAction" name="ManagerAction">
      <signalEventDefinition signalRef="ProcessSignal_ManagerAction"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow_NotifyManager" sourceRef="serviceTask_Notifymanager" targetRef="ManagerAction"/>

    <serviceTask id="serviceTask_FetchApplicationAfterManager" name="Fetch Application (Manager Review)" flowable:delegateExpression="${fetchBackOfficeDataDelegate}"/>
    <sequenceFlow id="flow_ManagerActionToFetchApplication" sourceRef="ManagerAction" targetRef="serviceTask_FetchApplicationAfterManager"/>
    <sequenceFlow id="flow_ManagerBackofficeDataToCheckStatus" sourceRef="serviceTask_FetchApplicationAfterManager" targetRef="checkStatusAfterManager"/>

    <exclusiveGateway id="checkStatusAfterManager" name="Manager Check Status" default="flow_ManagerToCheckRejectionOrInfo"/>
    <sequenceFlow id="flow_ManagerApprovedToCeoSignal" name="Approved" sourceRef="checkStatusAfterManager" targetRef="serviceTask_NotifyandEnd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'completed'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_ManagerToCheckRejectionOrInfo" sourceRef="checkStatusAfterManager" targetRef="checkRejectionOrInfoAfterManager"/>

    <serviceTask id="serviceTask_NotifyandEnd" name="Notify Client and End" flowable:delegateExpression="${notifyLeadDelegate}"/>
    <sequenceFlow id="flow_NotifyClientToEnd" sourceRef="serviceTask_NotifyandEnd" targetRef="endEvent_ProcessCompleted"/>

    <exclusiveGateway id="checkRejectionOrInfoAfterManager" name="Manager Check Rejection or Info Request"/>
    <sequenceFlow id="flow_manager_rejected" sourceRef="checkRejectionOrInfoAfterManager" targetRef="managersendRejectionNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'escalated'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow_manager_info_request" sourceRef="checkRejectionOrInfoAfterManager" targetRef="managersendInfoRequestNotification">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${status == 'info-request'}]]></conditionExpression>
    </sequenceFlow>

    <serviceTask id="managersendRejectionNotification" name="Send Rejection Notification to client" flowable:delegateExpression="${rejectionNotificationDelegate}"/>
    <sequenceFlow id="flow_ManagerRejectionToEnd" sourceRef="managersendRejectionNotification" targetRef="endEvent_ProcessCompleted"/>

    <serviceTask id="managersendInfoRequestNotification" name="Send Info Request Notification to Officer" flowable:delegateExpression="${infoRequestNotificationDelegate}"/>
    <sequenceFlow id="flow_ManagerInfoRequestToOfficer" sourceRef="managersendInfoRequestNotification" targetRef="ManagerAction"/>

    <endEvent id="endEvent_ProcessCompleted" name="End"/>
  </process>
</definitions>