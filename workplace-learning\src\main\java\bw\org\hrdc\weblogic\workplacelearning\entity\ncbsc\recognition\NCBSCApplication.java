package bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.base.Assignment;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Entity representing a Non-Credit Bearing Short Course Application.
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "ncbsc_application")
@Getter @Setter
@NamedEntityGraph(name = "Application",
        attributeNodes = {
                @NamedAttributeNode("scopeOfAccreditation"),
                @NamedAttributeNode("shortCourseInformation"),
                @NamedAttributeNode("courseContentAndDelivery"),
                @NamedAttributeNode("courseDeliverySchedule")
        }
)
public class NCBSCApplication extends Assignment implements Serializable {

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "application_number")
    private String applicationNumber;

    @Column(name = "organisation_id", nullable = false)
    private String organisationId;

    @Column(name = "assessment_purpose")
    private String trainingNeedsAssessmentPurpose;

    @Column(name = "skills_needs_analysis")
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;

    @Column(name = "short_course_delivery_mode")
    private String shortCourseDeliveryMode;

    @Column(name = "key_facilitation")
    private String keyFacilitation;

    @Column(name = "skills_assessment")
    private String assessmentType;

    @Column(name = "certification_assessment")
    private String certification;

    @Column(name = "third_party_arrangements")
    private String thirdPartyArrangements;

    @Column(name = "resources", columnDefinition = "TEXT")
    private String resources;

    @Column(name = "short_course_endorsement")
    private String shortCourseEndorsement;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_submitted", nullable = false)
    private LocalDateTime dateSubmitted;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_status", nullable = false)
    private Enums.Status applicationStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "application_state", nullable = false)
    private Enums.State applicationState;

    @OneToOne(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    @ToString.Exclude
    private ShortCourseInformation shortCourseInformation;

    @OneToOne(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @ToString.Exclude
    private CourseContentDelivery courseContentAndDelivery;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    @ToString.Exclude
    private Set<ScopeOfAccreditation> scopeOfAccreditation;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    @ToString.Exclude
    private Set<CourseDeliverySchedule> courseDeliverySchedule;

    @Column(name = "process_instance_id")
    private String processInstanceId;
}