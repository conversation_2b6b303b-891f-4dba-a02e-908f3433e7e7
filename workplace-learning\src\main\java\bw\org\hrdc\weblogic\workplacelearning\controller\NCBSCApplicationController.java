package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.common.Quotation;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.CourseDeliverySchedule;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ScopeOfAccreditation;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.service.common.QuotationService;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NCBSCApplicationCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NCBSCApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 20/02/25 18:47
 * @UpdatedBy martinspectre
 * @UpdatedOn 20/02/25 18:47
 */
@RestController
@RequestMapping("/api/v1/ncbsc/applications/recognition")
public class NCBSCApplicationController {
    private static final Logger logger = LoggerFactory.getLogger(NCBSCApplicationController.class);
    @Autowired
    private NCBSCApplicationService applicationService;

    @Autowired
    private NCBSCApplicationCommentsService commentsService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private WorkflowClient workflowClient;
    @Autowired
    private QuotationService quotationService;

    @PostMapping("/")
    public ResponseEntity<?> createApplication(@RequestBody NCBSCApplicationDto applicationDto) {
        logger.info("Application creation initiated for organization ID: {}", applicationDto.getOrganisationId());
        try {
            NCBSCApplicationDto savedApplication = applicationService.createApplication(applicationDto);
            if (savedApplication == null) {
                logger.error("New application for company id {} failed to create", applicationDto.getOrganisationId());
                return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create application.");
            }

            logger.info("New application for company id {} created successfully with reference {}", 
                    savedApplication.getOrganisationId(), savedApplication.getReferenceNumber());

            if (savedApplication.getApplicationState().equals(Enums.State.SUBMITTED)) {
               try {
                    Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(Enums.ApplicationType.RECOGNITION.name(), savedApplication.getReferenceNumber());
                    
                    logger.info("Workflow response: {}", workflowResponse);
                    if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                        Object data = workflowResponse.get("applicationData");
                        if (data != null) {
                            String processInstanceId = (String) ((Map<String, Object>)workflowResponse).get("processInstanceId");
                            // Save the processInstanceId to the database
                            String referenceid = savedApplication.getReferenceNumber();
                            applicationService.updateProcessInstanceId(referenceid, processInstanceId);
                            logger.info("Process instance ID {} saved for application {}", processInstanceId, savedApplication.getReferenceNumber());
                        }
                    }
                } catch (Exception e) {
                    logger.error("Failed to start workflow process: {}", e.getMessage());
                    // Continue execution even if workflow process fails
                }
            }
            
            return ResponseEntity.ok(new ApiResponse<>(true, "Application created successfully", savedApplication, null));

        } catch (Exception e) {
            logger.error("Application for recognition of company id {} failed with exception: {}", 
                    applicationDto.getOrganisationId(), e.getMessage(), e);
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/fetch")
    public ResponseEntity<ApiResponse<?>> getApplicationsByFilters(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) UUID companyId,
            @RequestParam(required = false) String applicationStatus,
            @RequestParam(required = false) String applicationState,
            @RequestParam(required = false) String trainingProvider,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String referenceNumber,
            @RequestParam(required = false) String courseTitle,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("Fetching NCBSC applications with criteria - role: {}, userId: {}, companyId: {}, status: {}, state: {}, " +
                "trainingProvider: {}, courseTitle: {}, referenceNumber: {}, dateRange: [{} to {}], search: {}",
                role, userId, companyId, applicationStatus, applicationState, trainingProvider, 
                courseTitle, referenceNumber, startDate, endDate, search);
        
        try {
            // For the frontend, we'll use a special format in the search parameter: "STATUS:STATE"
            if (search != null && search.contains(":")) {
                String[] parts = search.split(":", 2);
                if (parts.length == 2) {
                    // Convert to uppercase to match enum values
                    applicationStatus = parts[0].trim().toUpperCase();
                    applicationState = parts[1].trim().toUpperCase();
                    logger.info("Extracted from search parameter: status={}, state={}", applicationStatus, applicationState);
                    
                    // Clear search to avoid double filtering
                    search = null;
                }
            }
            
            // Delegate to service layer 
            Map<String, Object> response = applicationService.getApplicationsByFilters(
                    role, userId, companyId, applicationStatus, applicationState, trainingProvider,
                    startDate, endDate, referenceNumber, courseTitle, search, pageNumber, size);
            
            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            logger.error("Failed to fetch applications with exception: ", e);
            String errorMessage = String.format("Failed to fetch applications: %s", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, errorMessage, null, null));
        }
    }
    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllApplicationsWithSearch(@PathVariable Integer offset, @PathVariable Integer pageSize,
                                                             @RequestBody ApplicationSearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }
            
            logger.info("Searching applications with criteria - status: {}, state: {}, assignedTo: {}, courseTitle: {}, referenceNumber: {}, dateRange: [{} to {}]",
                    searchCriteria.getStatus(), searchCriteria.getState(), searchCriteria.getAssignedTo(),
                    searchCriteria.getCourseTitle(), searchCriteria.getReferenceNumber(), 
                    searchCriteria.getStartDate(), searchCriteria.getEndDate());

            // Create sort object based on criteria
            Sort sort;
            
            if (searchCriteria.getSortFields() != null && searchCriteria.getSortFields().length > 0) {
                // Handle multiple sort fields
                Sort.Direction sortDirection = Sort.Direction.fromString(searchCriteria.getDirection().toUpperCase());
                
                // Create sort orders for each field
                List<Sort.Order> orders = new ArrayList<>();
                for (String field : searchCriteria.getSortFields()) {
                    orders.add(new Sort.Order(sortDirection, field));
                }
                
                sort = Sort.by(orders);
                logger.info("Sorting by multiple fields: {}, direction: {}", 
                    String.join(", ", searchCriteria.getSortFields()), sortDirection);
            } else {
                // Single field sort
                Sort.Direction sortDirection = Sort.Direction.fromString(searchCriteria.getDirection().toUpperCase());
                sort = Sort.by(sortDirection, searchCriteria.getSortBy());
                logger.info("Sorting by field: {}, direction: {}", searchCriteria.getSortBy(), sortDirection);
            }

            Page<NCBSCApplicationListDto> applications = applicationService.getAllApplications(
                    searchCriteria, PageRequest.of(offset, pageSize, sort));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());
            
            // Add sorting information
            Map<String, Object> sortInfo = new HashMap<>();
            sortInfo.put("currentSortField", searchCriteria.getSortBy());
            sortInfo.put("currentSortDirection", searchCriteria.getDirection());
            
            // Available sort fields
            List<String> availableSortFields = Arrays.asList(
                "dateSubmitted", "referenceNumber", "applicationNumber", 
                "applicationStatus", "applicationState", "organisationId"
            );
            sortInfo.put("availableSortFields", availableSortFields);
            
            metadata.put("sort", sortInfo);

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }
    @GetMapping("/{referenceNumber}")
    public ResponseEntity<ApiResponse<?>> getApplicationByReferenceNumber(@PathVariable String referenceNumber) {
        try {
            Optional<NCBSCApplication> application = applicationService.getApplicationByReference(referenceNumber);
            Map<String, Object> applicationResponse = new HashMap<>(Map.of());
            if(application.isPresent()){
                applicationResponse.put("application", application);
                if(application.get().getCourseContentAndDelivery() != null){
                    List<Map<String, Object>> learningOutcomes = applicationService.getLearningOutcomes(application.get().getCourseContentAndDelivery().getUuid());
                    applicationResponse.put("learningOutcomes", learningOutcomes);
                }

                List<ScopeOfAccreditation> scopeOfAccreditations = applicationService.getScopeOfAccreditation(application.get().getUuid());
                if(!scopeOfAccreditations.isEmpty()){
                    applicationResponse.put("scopeOfAccreditations", scopeOfAccreditations);
                }

                List<CourseDeliverySchedule> courseDeliverySchedule = applicationService.getCourseDeliverySchedule(application.get().getUuid());
                if(!courseDeliverySchedule.isEmpty()){
                    applicationResponse.put("courseDeliverySchedule", courseDeliverySchedule);
                }

                //TODO get application comments

                List<NCBSCApplicationComments> applicationComments = commentsService.getAuditLogsForApplication(application.get().getUuid());
                if(!applicationComments.isEmpty()){
                    applicationResponse.put("comments", applicationComments);
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", applicationResponse, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateApplication(@PathVariable String id, @RequestBody RecognitionUpdateDto applicationDto) {
        try {
            RecognitionUpdateDto updatedApplication = applicationService.updateApplication(id, applicationDto);
            if(updatedApplication != null){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(true, "Update successful", updatedApplication,null));
            }
            return ApiResponse.createErrorResponse("APPLICATION_UPDATE_FAILED", "Application details failed to update");
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @DeleteMapping("/{referenceNumber}")
    public ResponseEntity<?> deleteApplication(@PathVariable String referenceNumber) {
        try {
            if (applicationService.getApplicationByReference(referenceNumber).isPresent()) {
                applicationService.deleteApplication(referenceNumber);
                //TODO Trigger email and/or sms notification to the company owner about application removal
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(true, "Application removed successful", null,null));

            } else {
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application identifier does not exist");
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationReference}/assign-user")
    public ResponseEntity<?> assignAgent(@PathVariable String applicationReference, @RequestBody Map<String, Object> payload) {
        logger.info("Application user assignment initiated for application reference : {}", applicationReference);
        try {
            String role = payload.get("role").toString();
            String userId = payload.get("userId").toString();

            if(role.isEmpty() || userId.isEmpty()){
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", "Required fields are missing, please provide user id and their respective role");
            }

            Optional<NCBSCApplication> application = applicationService.getApplicationByReference(applicationReference);

            if (application.isPresent()) {
                int updatedResult = applicationService.updateApplicationAssignedUser(applicationReference, Enums.UserRoles.valueOf(role), userId);
                if (updatedResult == 0) {
                    logger.error("Failed to update application reference of {}", applicationReference);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                }else{
                    //TODO trigger notification to agent for the assignment made

                     if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.get().getReferenceNumber(),
                                    application.get().getUuid(),
                                    application.get().getApplicationStatus().toString(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.RECOGNITION.name()
                                    
                            );
                    } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.get().getReferenceNumber(),
                                    application.get().getUuid(),
                                    application.get().getApplicationStatus().toString(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.RECOGNITION.name()
                            );
                    } 
                    return ResponseEntity.ok(new ApiResponse<>(true, "Application assigned successfully", null, null));
                }
            } else {
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to assign application reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationReference}/status-update")
    public ResponseEntity<?> changeApplicationStatus(@PathVariable String applicationReference, @RequestBody ApplicationStatusUpdatePayload payload) {
        logger.info("Application status update initiated for application reference : {}", applicationReference);
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();

            String actionType = null;

            if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)){
                actionType = "Agent_action";
            }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)){
                actionType = "Officer_action";
            }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)){
                 logger.info("manager action");
                actionType = "Manager_action";
            }

            if(action == null || action.isEmpty() || userId == null || userId.isEmpty()){
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", "Required fields are missing, please provide user id and their respective role");
            }

            Optional<NCBSCApplication> application = applicationService.getApplicationByReference(applicationReference);

            if (application.isPresent()) {
                NCBSCApplication existingApplication = application.get();
                int updatedResult = applicationService.updateApplicationStatus(applicationReference, role, action);
                if (updatedResult == 0) {
                    logger.error("Failed to update status of application reference of {}", applicationReference);
                    return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
                } else {
                    NCBSCApplicationComments logEntry = new NCBSCApplicationComments();
                    logEntry.setNcbscApplication(existingApplication);
                    logEntry.setAction(action);
                    // Ensure comments is not null before sanitizing
                    logEntry.setComments(comments != null ? applicationService.sanitizeHtml(comments) : "");
                    logEntry.setUpdatedBy(userId);
                    logEntry.setTimestamp(LocalDateTime.now());

                    commentsService.createComments(logEntry);
                    
                    // Check if processInstanceId exists before using it
                    String processInstanceId = existingApplication.getProcessInstanceId();
                    logger.info("processInstanceId : {}", processInstanceId);
                    if (processInstanceId != null && !processInstanceId.isEmpty()) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("referenceNumber", applicationReference);
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.RECOGNITION.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, applicationReference);
                            workflowClient.resumeProcess(processInstanceId, actionType, workflowPayload);
                        } catch (Exception e) {
                            // Log the error but continue with the application status update
                            logger.error("Failed to resume workflow process: {}", e.getMessage());
                            // Don't rethrow the exception - allow the application status update to succeed
                        }
                    } else {
                        logger.warn("No process instance ID found for application {}", applicationReference);
                    }
                    
                    return ResponseEntity.ok(new ApiResponse<>(true, "Application status updated successfully", null, null));
                }
            } else {
                return ApiResponse.createErrorResponse("APPLICATION_NOT_FOUND", "Provided application identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to update status of application reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/batch-status-update")
    public ResponseEntity<?> batchChangeApplicationStatus(@RequestBody BatchStatusUpdateRequest payload) {
        logger.info("Batch application status update initiated for {} applications", 
                payload.getApplicationReferences() != null ? payload.getApplicationReferences().size() : 0);
        
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            List<String> applicationReferences = payload.getApplicationReferences();

            if (action.isEmpty() || userId.isEmpty() || applicationReferences == null || applicationReferences.isEmpty()) {
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", 
                        "Required fields are missing, please provide user id, action and application references");
            }

            BatchStatusUpdateResult result = applicationService.batchUpdateApplicationStatus(
                    applicationReferences, role, action, userId, comments);
            
            return ResponseEntity.ok(new ApiResponse<>(true, 
                    "Batch status update processed: " + result.getSuccessCount() + " successful, " + 
                    result.getFailureCount() + " failed", result, null));
            
        } catch (Exception e) {
            logger.error("Failed to process batch status update with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }


    @GetMapping("/company/{companyId}/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllCompanyApplications(@PathVariable String companyId, @PathVariable Integer offset, @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<NCBSCApplicationListDto> applications = applicationService.getAllCompanyApplications(companyId, PageRequest.of(offset, pageSize));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/application-number/{applicationNumber}")
    public ResponseEntity<ApiResponse<?>> getApplicationByApplicationNumber(@PathVariable String applicationNumber) {
        try {
            Optional<NCBSCApplication> application = applicationService.getApplicationByApplicationNumber(applicationNumber);
            Map<String, Object> applicationResponse = new HashMap<>(Map.of());
            if(application.isPresent()){
                applicationResponse.put("application", application);
                if(application.get().getCourseContentAndDelivery() != null){
                    List<Map<String, Object>> learningOutcomes = applicationService.getLearningOutcomes(application.get().getCourseContentAndDelivery().getUuid());
                    applicationResponse.put("learningOutcomes", learningOutcomes);
                }

                List<ScopeOfAccreditation> scopeOfAccreditations = applicationService.getScopeOfAccreditation(application.get().getUuid());
                if(!scopeOfAccreditations.isEmpty()){
                    applicationResponse.put("scopeOfAccreditations", scopeOfAccreditations);
                }

                List<CourseDeliverySchedule> courseDeliverySchedule = applicationService.getCourseDeliverySchedule(application.get().getUuid());
                if(!courseDeliverySchedule.isEmpty()){
                    applicationResponse.put("courseDeliverySchedule", courseDeliverySchedule);
                }

                //TODO get application comments

                List<NCBSCApplicationComments> applicationComments = commentsService.getAuditLogsForApplication(application.get().getUuid());
                if(!applicationComments.isEmpty()){
                    applicationResponse.put("comments", applicationComments);
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", applicationResponse, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationId}/quotation-accept/{quotationId}/{status}")
    @Transactional
    public ResponseEntity<?> acceptQuotation(@PathVariable String applicationId, @PathVariable String quotationId, @PathVariable boolean status) {
        logger.info("NOC Application quote accept status initiated");
        try {
            Optional<NCBSCApplication> applicationObj = applicationService.getApplicationById(applicationId);

            if (applicationObj.isEmpty()) {
                return ApiResponse.createErrorResponse("NOT_FOUND", "Application not found with id: " + applicationId);
            }
            NCBSCApplication application = applicationObj.get();
            if(status){
                String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                application.setReferenceNumber(referenceNumber);
                application.setApplicationState(Enums.State.SUBMITTED);
                application.setApplicationStatus(Enums.Status.PENDING_PAYMENT);

                int ncbscApplication = applicationService.upgradeApplication(applicationId, referenceNumber, Enums.State.SUBMITTED.name(), Enums.Status.PENDING_PAYMENT.name());
                if (ncbscApplication < 1) {
                    logger.error("Failed to update NCBSC application for company id {} when accepting quotation", application.getOrganisationId());
                    return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to Accept the quotation.");
                }

                logger.info("Application for company id {} updated successfully, reference number generated and application submitted", application.getOrganisationId());
                //Update Quotation
                Map<String, Object> response = new HashMap<>();
                response.put("application", application);

                Quotation quotation = quotationService.acceptQuote(quotationId, true);
                if(quotation != null){
                    logger.info("Quotation for company id {} and application reference number {} has been updated successfully", application.getOrganisationId(), referenceNumber);
                    response.put("quotation", quotation);
                }else{
                    logger.error("Quotation for company id {} and application reference number {} failed to update", application.getOrganisationId(), referenceNumber);
                }
                //TODO Do auto assignment of the application to the agent through the workflow engine

                //TODO Trigger email/sms and in-app notification about accepted quotation.

                return ResponseEntity.ok(new ApiResponse<>(true, "Quotation has been accepted successfully", response, null));
            }else{
                //Quotation not accepted, remove the quotation and NOC application
                applicationService.deleteApplication(applicationId);
                quotationService.deleteById(quotationId);
                return ResponseEntity.ok(new ApiResponse<>(true, "Quotation has been rejected by the user", null, null));
            }
        }catch (Exception exception){
            logger.error("Quotation status update for Application id {} failed with exception: {}", applicationId, exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }
}
