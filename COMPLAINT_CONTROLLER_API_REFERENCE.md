# Complaint Controller API Reference

## Base URL
All endpoints are prefixed with: `http://localhost:8091/api/v1/complaints`

## 📋 **Core Complaint Endpoints**

### 1. Get Complaint by ID
```bash
GET /api/v1/complaints/{id}
```
**Response:**
```json
{
  "status": true,
  "message": "Record found",
  "data": {
    "uuid": "cc610b03-df26-410b-8623-236e47afdc6c",
    "organisationId": "org-123",
    "department": "TECHNICAL",
    "typeOfComplaint": "SERVICE_QUALITY",
    "description": "Service quality issue",
    "referenceNumber": "REF-2025-001",
    "status": "OPEN",
    "state": "SUBMITTED",
    "assignedTo": "agent-123",
    "createdAt": "2025-06-16T06:00:00Z",
    "updatedAt": "2025-06-16T06:00:00Z",
    "createdBy": "user-123",
    "updatedBy": "user-123"
  },
  "errors": null
}
```

### 2. Create Complaint
```bash
POST /api/v1/complaints
Headers: X-Authenticated-User: {userId}
```

### 3. Get All Complaints (Paginated)
```bash
GET /api/v1/complaints/{offset}/{pageSize}
```

## 🔄 **Status Management Endpoints**

### 1. Update Status (PATCH - Original)
```bash
PATCH /api/v1/complaints/{id}/status
Headers: X-Authenticated-User: {userId}
Content-Type: application/json

{
  "status": "IN_PROGRESS",
  "state": "UNDER_REVIEW"
}
```

### 2. Update Status (PUT - Workflow Integration)
```bash
PUT /api/v1/complaints/{id}/status
Content-Type: application/json

{
  "status": "CLOSED",
  "state": "COMPLETED",
  "userId": "SYSTEM"
}
```

## 🚀 **Action Endpoints**

### 1. Escalate Complaint (PATCH - Original)
```bash
PATCH /api/v1/complaints/{id}/escalate
Headers: X-Authenticated-User: {userId}
```

### 2. Escalate Complaint (PUT - Workflow Integration)
```bash
PUT /api/v1/complaints/{id}/escalate
Content-Type: application/json

{
  "userId": "SYSTEM",
  "reason": "Escalated via workflow"
}
```

### 3. Close Complaint (PATCH - Original)
```bash
PATCH /api/v1/complaints/{id}/close
Headers: X-Authenticated-User: {userId}
```

### 4. Close Complaint (PUT - Workflow Integration)
```bash
PUT /api/v1/complaints/{id}/close
Content-Type: application/json

{
  "userId": "SYSTEM",
  "reason": "Closed via workflow"
}
```

### 5. Assign Complaint (PATCH - Original)
```bash
PATCH /api/v1/complaints/{id}/assign-agent/{agentId}
Headers: X-Authenticated-User: {userId}
```

### 6. Assign Complaint (PUT - Workflow Integration)
```bash
PUT /api/v1/complaints/{id}/assign
Content-Type: application/json

{
  "agentId": "agent-123",
  "userId": "SYSTEM"
}
```

### 7. Communicate with Client
```bash
PATCH /api/v1/complaints/{id}/communicate
Headers: X-Authenticated-User: {userId}
```

### 8. Client Response
```bash
PATCH /api/v1/complaints/{id}/client-response
Headers: X-Authenticated-User: {userId}
```

## 💬 **Comments Endpoint**

### Add Comment
```bash
POST /api/v1/complaints/{complaintId}/comments
Headers: X-Authenticated-User: {userId}
Content-Type: application/json

{
  "comment": {
    "content": "This is a comment",
    "type": "INTERNAL"
  },
  "documents": []
}
```

## 📊 **Query Endpoints**

### 1. Get Complaints by Role
```bash
GET /api/v1/complaints/fetch?role={role}&userId={userId}&status={status}&assignedTo={assignedTo}&search={search}&pageNumber=0&size=10
```

### 2. Get Company Complaints
```bash
GET /api/v1/complaints/{offset}/{pageSize}/{companyId}
```

### 3. Get Statistics
```bash
GET /api/v1/complaints/statistics
GET /api/v1/complaints/{companyId}/statistics
```

## 🔧 **Workflow Integration Mapping**

The WorkplaceLearningClient in the workflow service maps to these endpoints:

| Workflow Client Method | Complaint Controller Endpoint |
|------------------------|-------------------------------|
| `getComplaintById(id)` | `GET /api/v1/complaints/{id}` |
| `updateComplaintStatus(id, data)` | `PUT /api/v1/complaints/{id}/status` |
| `escalateComplaint(id, data)` | `PUT /api/v1/complaints/{id}/escalate` |
| `closeComplaint(id, data)` | `PUT /api/v1/complaints/{id}/close` |
| `assignComplaint(id, data)` | `PUT /api/v1/complaints/{id}/assign` |
| `addComplaintComment(id, data)` | `POST /api/v1/complaints/{id}/comments` |

## 📝 **Response Formats**

### Success Response
```json
{
  "status": true,
  "message": "Operation successful",
  "data": { /* complaint data */ },
  "errors": null
}
```

### Error Response
```json
{
  "status": false,
  "message": "Error message",
  "data": null,
  "errors": ["Error details"]
}
```

## 🎯 **Complaint Entity Structure**

```json
{
  "uuid": "string",
  "organisationId": "string",
  "department": "TECHNICAL|FINANCE|HR|MANAGEMENT",
  "typeOfComplaint": "string",
  "description": "string",
  "reference": "string",
  "referenceNumber": "string",
  "status": "OPEN|IN_PROGRESS|AWAITING_CLIENT_FEEDBACK|CLOSED",
  "state": "SUBMITTED|UNDER_REVIEW|ESCALATED|COMPLETED",
  "assignee": "string",
  "category": "COMPLAINT|APPEAL",
  "processInstanceId": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "createdBy": "string",
  "updatedBy": "string"
}
```

## 🔄 **Status and State Enums**

### ComplaintStatus
- `OPEN` - Complaint is open and active
- `IN_PROGRESS` - Being worked on
- `AWAITING_CLIENT_FEEDBACK` - Waiting for client response
- `CLOSED` - Complaint resolved and closed

### ComplaintState
- `SUBMITTED` - Initial submission
- `UNDER_REVIEW` - Being reviewed by agent
- `ESCALATED` - Escalated to agent lead
- `COMPLETED` - Fully resolved

## 🚨 **Important Notes**

1. **Dual Endpoints**: Most actions have both PATCH (original) and PUT (workflow) versions
2. **Authentication**: Original endpoints require `X-Authenticated-User` header
3. **Workflow Integration**: PUT endpoints accept `userId` in request body for system operations
4. **Fallback Handling**: All endpoints return proper error responses when services are unavailable
5. **Audit Logging**: All operations are logged in the audit trail
6. **Notifications**: Automatic notifications are sent for assignments and status changes
