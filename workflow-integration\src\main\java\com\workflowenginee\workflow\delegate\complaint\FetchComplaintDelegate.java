package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@Component("fetchComplaintDelegate")
public class FetchComplaintDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            log.info("Fetching complaint details for ID: {}", complaintId);

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // Call workplace learning service to fetch complaint details
            String url = "http://localhost:8091/api/v1/complaints/" + complaintId;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> complaintData = response.getBody();
                
                // Store complaint data in process variables
                execution.setVariable("complaintData", complaintData);
                execution.setVariable("organisationId", complaintData.get("organisationId"));
                execution.setVariable("status", complaintData.get("status"));
                execution.setVariable("state", complaintData.get("state"));
                execution.setVariable("assignee", complaintData.get("assignee"));
                execution.setVariable("department", complaintData.get("department"));
                execution.setVariable("typeOfComplaint", complaintData.get("typeOfComplaint"));
                execution.setVariable("description", complaintData.get("description"));
                execution.setVariable("referenceNumber", complaintData.get("referenceNumber"));
                
                log.info("Successfully fetched complaint data for ID: {}", complaintId);
            } else {
                log.error("Failed to fetch complaint data for ID: {}", complaintId);
                throw new RuntimeException("Failed to fetch complaint data");
            }

        } catch (Exception e) {
            log.error("Error fetching complaint details: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to fetch complaint details: " + e.getMessage());
            throw new RuntimeException("Error in FetchComplaintDelegate", e);
        }
    }
}
