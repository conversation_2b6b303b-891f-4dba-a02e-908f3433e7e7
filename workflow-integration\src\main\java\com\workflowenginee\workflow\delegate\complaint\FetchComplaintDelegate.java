package com.workflowenginee.workflow.delegate.complaint;

import java.util.HashMap;
import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;

@Component("fetchComplaintDelegate")
public class FetchComplaintDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;

    public FetchComplaintDelegate(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Fetching complaint details for ID: " + complaintId);

        try {
            ApiResponse<?> response = workplaceLearningClient.getComplaintById(complaintId);

            if (response != null && response.isStatus() && response.getData() instanceof Map) {
                // Successful response from service
                Map<String, Object> complaintData = (Map<String, Object>) response.getData();
                execution.setVariable("complaintData", complaintData);
                execution.setVariable("complaintStatus", complaintData.get("status"));
                execution.setVariable("complaintState", complaintData.get("state"));
                execution.setVariable("assignee", complaintData.get("assignee"));
                execution.setVariable("organisationId", complaintData.get("organisationId"));
                execution.setVariable("referenceNumber", complaintData.get("referenceNumber"));

                System.out.println("[Process: " + processInstanceId + "] Successfully fetched complaint data");
                System.out.println("Complaint Data: " + complaintData);

            } else {
                // Service unavailable or failed response (fallback triggered)
                System.err.println("[Process: " + processInstanceId + "] Service unavailable or failed response");
                if (response != null) {
                    System.err.println("Response message: " + response.getMessage());
                }

                // Create fallback data for workflow to continue
                Map<String, Object> fallbackData = new HashMap<>();
                fallbackData.put("id", complaintId);
                fallbackData.put("status", "OPEN");
                fallbackData.put("state", "SUBMITTED");
                fallbackData.put("assignee", "SYSTEM");
                fallbackData.put("typeOfComplaint", "GENERAL");
                fallbackData.put("priority", "MEDIUM");
                fallbackData.put("department", "GENERAL");
                fallbackData.put("message", "Service unavailable - using fallback data");

                execution.setVariable("complaintData", fallbackData);
                execution.setVariable("complaintStatus", "OPEN");
                execution.setVariable("complaintState", "SUBMITTED");
                execution.setVariable("assignee", "SYSTEM");
                execution.setVariable("serviceUnavailable", true);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error fetching complaint data: " + e.getMessage());
            e.printStackTrace();

            // Set error data
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("complaintId", complaintId);
            errorData.put("status", "ERROR");
            errorData.put("error", e.getMessage());
            
            execution.setVariable("complaintData", errorData);
        }
    }
}
