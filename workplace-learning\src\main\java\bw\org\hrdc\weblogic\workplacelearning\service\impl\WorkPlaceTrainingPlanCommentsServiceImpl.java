package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanCommentsRepository;
import bw.org.hrdc.weblogic.workplacelearning.service.WorkPlaceTrainingPlanCommentsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class WorkPlaceTrainingPlanCommentsServiceImpl implements WorkPlaceTrainingPlanCommentsService {

    private final WorkPlaceTrainingPlanCommentsRepository commentsRepository;

    @Override
    public WorkPlaceTrainingPlanComments createComments(WorkPlaceTrainingPlanComments comment) {
        return commentsRepository.save(comment);
    }

    @Override
    public List<WorkPlaceTrainingPlanComments> getAuditLogsForTrainingPlan(UUID trainingPlanId) {
        return commentsRepository.findByTrainingPlanIdOrderByTimestampDesc(trainingPlanId);
    }
}