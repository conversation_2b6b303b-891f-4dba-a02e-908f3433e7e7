package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.dto.NotificationDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class NotificationService {
    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    @Qualifier("notificationKafkaTemplate")
    private KafkaTemplate<String, NotificationDTO> kafkaTemplate;
    
    @Autowired
    private CompanyClient companyClient;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${spring.kafka.template.default-topic}")
    private String notificationTopic;

    /**
     * Sends application notifications to all users with a specific role and notification type
     * 
     * @param role The role to send notifications to
     * @param referenceNumber The application reference number
     * @param applicationId The application ID
     * @param applicationStatus The application status
     * @param notificationType The type of notification (IN_APP or EMAIL)
     */
    public void notifyUsersByRole(String role, String referenceNumber, String applicationId, 
                                 String applicationStatus, String notificationType, String applicationType) {
        
        logger.info("Sending {} notifications to users with role: {}", notificationType, role);
        
        try {
            // Call the API to get users by role using Feign client
            ApiResponse<?> response = companyClient.fetchAllActiveUsers(role);
            
            if (response != null && response.isStatus() && response.getData() != null) {
                List<Map<String, Object>> users = (List<Map<String, Object>>) response.getData();
                
                for (Map<String, Object> user : users) {
                    String userId = (String) user.get("id");
                    String username = (String) user.get("username");
                    
                    // Send notification to each user with specified type
                    sendApplicationNotification(userId, username, referenceNumber, applicationId, 
                                              applicationStatus, notificationType, applicationType);
                }
                
                logger.info("Successfully sent {} notifications to {} users with role {}", 
                          notificationType, users.size(), role);
            } else {
                logger.warn("Failed to retrieve users with role: {}", role);
            }
        } catch (Exception e) {
            logger.error("Error sending {} notifications to users with role {}: {}", 
                       notificationType, role, e.getMessage(), e);
        }
    }
    
    /**
     * Sends an application notification to a specific user with specified notification type
     */
    private void sendApplicationNotification(String userId, String username, String referenceNumber, 
                                            String applicationId, String applicationStatus, 
                                            String notificationType, String applicationType) {
        try {
            // Determine appropriate message based on application status
            String message;
            String subject;
            
            if (applicationStatus.equalsIgnoreCase(Enums.State.SUBMITTED.name())) {
                subject = "New Application Notification";
                message = "A new application " + referenceNumber + " has been created";
            } else if (applicationStatus.equalsIgnoreCase(Enums.Status.APPROVED.name())) {
                subject = "Application Approval Notification";
                message = "Application " + referenceNumber + " has been approved and requires your attention";
            } else if (applicationStatus.equalsIgnoreCase(Enums.Status.REJECTED.name())) {
                subject = "Application Rejection Notification";
                message = "Application " + referenceNumber + " has been rejected";
            } else if (applicationStatus.equalsIgnoreCase(Enums.Status.CHANGE_REQUEST.name())) {
                subject = "Application Change Request";
                message = "Application " + referenceNumber + " requires changes";
            } else {
                // Default message for other statuses
                subject = "Application Status Update";
                message = "Application " + referenceNumber + " has been assigned to you for review";
            }

            String appId = null;
            if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType)|| Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType) || Enums.ApplicationType.COMPLAINTS.name().equalsIgnoreCase(applicationType)) {
                appId =  applicationId;
            } else {
                appId = referenceNumber;
            }
                
            NotificationDTO notification = NotificationDTO.builder()
                .id(UUID.randomUUID().toString())
                .name(username)
                .subject(subject)
                .recipient(userId)
                .message(message)
                .sentAt(LocalDateTime.now().toString())
                .type(notificationType)
                .applicationId(appId)
                .applicationType(applicationType)
                .status("PENDING")
                .build();
            
            // Use userId as the key for message partitioning
            kafkaTemplate.send(notificationTopic, notification)
                .whenComplete((result, ex) -> {
                    if (ex == null) {
                        logger.info("Sent {} notification to user {} with offset=[{}]", 
                            notificationType, username, result.getRecordMetadata().offset());
                    } else {
                        logger.error("Failed to send {} notification to user {}: {}", 
                            notificationType, username, ex.getMessage());
                    }
                });
        } catch (Exception e) {
            logger.error("Error sending {} notification to user {}: {}", notificationType, username, e.getMessage());
        }
    }

    /**
     * Sends a notification to a specific user by userId
     * 
     * @param userId The user ID to send notification to
     * @param referenceNumber The application reference number
     * @param applicationId The application ID
     * @param applicationStatus The application status
     * @param notificationType The type of notification (IN_APP or EMAIL)
     */
    public void sendNotificationToUser(String userId, String referenceNumber, String applicationId, 
                                     String applicationStatus, String notificationType, String applicationType) {
        logger.info("Sending {} notification to user with ID: {}", notificationType, userId);
        System.out.println("userId :: "+ userId);
        
        try {
            // Call the API to get user details by userId
            ApiResponse<?> response = companyClient.fetchUserById(userId);
            
            if (response != null && response.isStatus() && response.getData() != null) {
                Map<String, Object> userData = (Map<String, Object>) response.getData();
                String username = (String) userData.get("username");
                
                if (username != null) {
                    // Send notification to the user
                    sendApplicationNotification(userId, username, referenceNumber, applicationId, 
                                              applicationStatus, notificationType, applicationType);
                    
                    logger.info("Successfully sent {} notification to user {}", notificationType, username);
                } else {
                    logger.warn("Username not found for user ID: {}", userId);
                }
            } else {
                logger.warn("Failed to retrieve user details for ID: {}", userId);
            }
        } catch (Exception e) {
            logger.error("Error sending {} notification to user ID {}: {}", 
                       notificationType, userId, e.getMessage(), e);
        }
    }
}
