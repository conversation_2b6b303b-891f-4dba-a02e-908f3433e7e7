package bw.org.hrdc.weblogic.workplacelearning.controller.common;

import bw.org.hrdc.weblogic.workplacelearning.entity.common.Quotation;
import bw.org.hrdc.weblogic.workplacelearning.service.common.QuotationService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 25/05/25 06:05
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/05/25 06:05
 */
@RestController
@RequestMapping("/api/v1/ncbsc/quotations")
public class QuotationController {
    @Autowired
    private QuotationService quoteService;

    @GetMapping("/{quoteNumber}")
    @Transactional
    public ResponseEntity<ApiResponse<?>> fetchByQuoteReference(@PathVariable String quoteNumber) {
        try {
            Optional<Quotation> application = quoteService.getQuoteByReference(quoteNumber);
            if(application.isPresent()){
                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", application, null));
            }else {
                return ResponseEntity.status(HttpStatus.OK).body(new ApiResponse<>(false, "No record found", null, null));
            }
        }catch (Exception exception){
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(false, "Internal server error occurred", null, null));
        }
    }

    @GetMapping("/ref-number/{reference}")
    @Transactional
    public ResponseEntity<ApiResponse<?>> fetchByReference(@PathVariable String reference) {
        try {
            Optional<Quotation> application = quoteService.getByReference(reference);
            if(application.isPresent()){
                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", application, null));
            }else {
                return ResponseEntity.status(HttpStatus.OK).body(new ApiResponse<>(false, "No record found", null, null));
            }
        }catch (Exception exception){
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(false, "Internal server error occurred", null, null));
        }
    }
}

