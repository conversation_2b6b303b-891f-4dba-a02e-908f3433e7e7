package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("agentChatWithClientDelegate")
public class AgentChatWithClientDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String assignee = (String) execution.getVariable("assignee");
            String chatMessage = (String) execution.getVariable("chatMessage");
            String organisationId = (String) execution.getVariable("organisationId");
            
            log.info("Agent {} initiating chat with client for complaint ID: {}", assignee, complaintId);

            // Prepare chat payload
            Map<String, Object> chatPayload = new HashMap<>();
            chatPayload.put("complaintId", complaintId);
            chatPayload.put("action", "AGENT_CHAT");
            chatPayload.put("performedBy", assignee);
            chatPayload.put("message", chatMessage != null ? chatMessage : "Agent is reaching out to discuss your complaint");
            chatPayload.put("status", "AWAITING_CLIENT_FEEDBACK");
            chatPayload.put("state", "IN_PROGRESS");
            chatPayload.put("chatInitiatedBy", "AGENT");

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(chatPayload, headers);

            // Update complaint status to indicate chat initiated
            String chatUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/chat";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                chatUrl, 
                HttpMethod.PUT, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully initiated chat for complaint {} by agent {}", complaintId, assignee);
                
                execution.setVariable("chatInitiated", true);
                execution.setVariable("chatInitiatedBy", "AGENT");
                execution.setVariable("lastAction", "AGENT_CHAT_INITIATED");
                execution.setVariable("chatTimestamp", System.currentTimeMillis());
                
                // Send notification to client about chat initiation
                sendChatNotification(execution, complaintId, assignee, organisationId);
                
                // Set status to awaiting client feedback
                execution.setVariable("awaitingClientFeedback", true);
                
            } else {
                log.error("Failed to initiate chat for complaint {} by agent {}", complaintId, assignee);
                throw new RuntimeException("Failed to initiate chat with client");
            }

        } catch (Exception e) {
            log.error("Error initiating chat with client: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to initiate chat: " + e.getMessage());
            throw new RuntimeException("Error in AgentChatWithClientDelegate", e);
        }
    }

    private void sendChatNotification(DelegateExecution execution, String complaintId, String agentId, String organisationId) {
        try {
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("organisationId", organisationId);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("agentId", agentId);
            notificationPayload.put("action", "CHAT_INITIATED");
            notificationPayload.put("message", "An agent would like to discuss your complaint with you");
            notificationPayload.put("type", "COMPLAINT_CHAT_REQUEST");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            restTemplate.exchange(notificationUrl, HttpMethod.POST, entity, Map.class);
            
            log.info("Chat notification sent for complaint {}", complaintId);
            
        } catch (Exception e) {
            log.warn("Failed to send chat notification for complaint {}: {}", complaintId, e.getMessage());
        }
    }
}
