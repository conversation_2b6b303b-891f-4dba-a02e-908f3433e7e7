package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.dto.common.CompanyDTO;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.*;
import bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc.*;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.*;
import bw.org.hrdc.weblogic.workplacelearning.util.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.Batch;
import bw.org.hrdc.weblogic.workplacelearning.repository.BatchRepository;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Subquery;
import jakarta.persistence.criteria.Root;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import jakarta.annotation.Nullable;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static bw.org.hrdc.weblogic.workplacelearning.util.Common.defaultId;
import static bw.org.hrdc.weblogic.workplacelearning.util.Common.defaultUuid;

@Service
@Slf4j
public class NCBSCApplicationService {

    @Autowired
    private NCBSCApplicationRepo applicationRepository;
    @Autowired
    private CompanyClient companyClient;
    @Autowired
    private ScopeOfAccreditationRepo scopeOfAccreditationRepository;
    @Autowired
    private CourseInformationRepo courseInformationRepo;
    @Autowired
    private CourseContentDeliveryRepo courseContentDeliveryRepo;
    @Autowired
    private CourseDeliveryScheduleRepo courseDeliveryScheduleRepo;
    @Autowired
    private LearningOutcomeRepo learningOutcomeRepo;
    @Autowired
    private AssessmentCriteriaRepo assessmentCriteriaRepo;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private NCBSCApplicationCommentsService commentsService;

    @Autowired
    private BatchRepository batchRepository;

    @Transactional
    public NCBSCApplicationDto createApplication(NCBSCApplicationDto applicationDto) {
        String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("app");
        applicationDto.setApplicationNumber(applicationNumber);
        applicationDto.setDateSubmitted(LocalDateTime.now());

        applicationDto.setApplicationState(Enums.State.DRAFT);
        applicationDto.setApplicationStatus(Enums.Status.INITIAL);

        NCBSCApplication application = applicationRepository.save(NCBSCApplicationMapper.toEntity(applicationDto));

        if (applicationDto.getScopeOfAccreditation() != null) {
            Set<ScopeOfAccreditation> scopeOfAccreditations =
                    ScopeOfAccreditationMapper.toEntityList(applicationDto.getScopeOfAccreditation(), application);
            scopeOfAccreditationRepository.saveAll(scopeOfAccreditations);
        }

        Optional.ofNullable(applicationDto.getShortCourseInformation())
                .map(info -> ShortCourseInformationMapper.toEntity(info, application))
                .ifPresent(courseInformationRepo::save);

        if (applicationDto.getCourseContentAndDelivery() != null) {
            CourseContentDeliveryDto courseContentAndDelivery = applicationDto.getCourseContentAndDelivery();

            CourseContentDelivery courseContent = CourseContentDeliveryMapper.toEntity(courseContentAndDelivery, application);

            courseContent = courseContentDeliveryRepo.save(courseContent);

            if (courseContentAndDelivery.getLearningOutcomes() != null) {
                for (LearningOutcomeDto learningOutcome : courseContentAndDelivery.getLearningOutcomes()) {
                    final LearningOutcome learningOutcomeEntity = learningOutcomeRepo.save(LearningOutcomeMapper.toEntity(learningOutcome, courseContent));

                    if (learningOutcome.getAssessmentCriteria() != null) {
                        Set<AssessmentCriteria> assessmentCriteria = AssessmentCriteriaMapper.toEntityList(learningOutcome.getAssessmentCriteria(), learningOutcomeEntity);
                        assessmentCriteriaRepo.saveAll(assessmentCriteria);
                    }
                }
            }
        }

        if (applicationDto.getCourseDeliverySchedule() != null) {
            Set<CourseDeliverySchedule> courseSchedules =
                    CourseDeliveryScheduleMapper.toEntityList(applicationDto.getCourseDeliverySchedule(), application);
            courseDeliveryScheduleRepo.saveAll(courseSchedules);
        }

        return NCBSCApplicationMapper.toDto(application);
    }

    public Page<NCBSCApplicationListDto> getAllApplications(PageRequest pageable) {

        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Page<NCBSCApplication> applications = applicationRepository.findAll(pageable);
        return applications.map(app -> {
            Enums.Status applicationStatus = app.getApplicationStatus();

            NCBSCApplicationListDto model = new NCBSCApplicationListDto();
            model.setReferenceNumber(app.getReferenceNumber());
            model.setApplicationNumber(app.getApplicationNumber());
            model.setOrganisationId(app.getOrganisationId());
            model.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
            model.setDateSubmitted(app.getDateSubmitted());
            model.setApplicationStatus(applicationStatus);
            model.setCourseTitle(app.getShortCourseInformation().getTitle() != null ? app.getShortCourseInformation().getTitle() : "");

            model.setApplicationStatus(app.getApplicationStatus());
            model.setApplicationState(app.getApplicationState());

            if(app.getApplicationState().equals(Enums.State.IN_APPROVAL) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedManager());
            }else if(app.getApplicationState().equals(Enums.State.IN_REVIEW) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedOfficer());
            }else if(app.getApplicationState().equals(Enums.State.IN_PROCESSING) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgent());
            }else if(app.getApplicationState().equals(Enums.State.SUBMITTED) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgentLead());
            }else{
                model.setAssignedTo(null);
            }

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setCompanyName(company.get(0).get("name").toString());
            }

            return model;
        });
    }

    private boolean isEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    public Page<NCBSCApplicationListDto> getAllApplications(ApplicationSearchCriteria searchCriteria, PageRequest pageable) {
        Specification<NCBSCApplication> spec = Specification.where(BaseSpecifications.isNotDeleted());

        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("applicationStatus"), ApplicationStatus.valueOf(searchCriteria.getStatus().toUpperCase())));
        }

        if (searchCriteria.getState() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("applicationState"), ApplicationStatus.valueOf(searchCriteria.getState().toUpperCase())));
        }

        if (searchCriteria.getAssignedTo() != null) {
            String assignedTo = searchCriteria.getAssignedTo();
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("assignedManager"), assignedTo),
                    criteriaBuilder.equal(root.get("assignedOfficer"), assignedTo),
                    criteriaBuilder.equal(root.get("assignedAgent"), assignedTo),
                    criteriaBuilder.equal(root.get("assignedAgentLead"), assignedTo)
            ));
        }
        
        // Filter by reference number
        if (searchCriteria.getReferenceNumber() != null && !searchCriteria.getReferenceNumber().isEmpty()) {
            spec = spec.and((root, query, cb) -> 
                cb.like(cb.lower(root.get("referenceNumber")), 
                    "%" + searchCriteria.getReferenceNumber().toLowerCase() + "%"));
        }
        
        // Filter by course title
        if (searchCriteria.getCourseTitle() != null && !searchCriteria.getCourseTitle().isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                Join<NCBSCApplication, ?> courseJoin = root.join("shortCourseInformation", JoinType.LEFT);
                return cb.like(cb.lower(courseJoin.get("title")), 
                        "%" + searchCriteria.getCourseTitle().toLowerCase() + "%");
            });
        }
        
        // Date range filter
        if (searchCriteria.getStartDate() != null && !searchCriteria.getStartDate().isEmpty() && 
            searchCriteria.getEndDate() != null && !searchCriteria.getEndDate().isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                Date startDate = sdf.parse(searchCriteria.getStartDate());
                Date endDate = sdf.parse(searchCriteria.getEndDate());
                
                spec = spec.and((root, query, cb) -> 
                    cb.between(root.get("dateSubmitted"), startDate, endDate));
            } catch (Exception e) {
                // Log error but continue without this filter
                log.error("Error parsing date range: {}", e.getMessage());
            }
        } else if (searchCriteria.getStartDate() != null && !searchCriteria.getStartDate().isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                Date startDate = sdf.parse(searchCriteria.getStartDate());
                
                spec = spec.and((root, query, cb) -> 
                    cb.greaterThanOrEqualTo(root.get("dateSubmitted"), startDate));
            } catch (Exception e) {
                log.error("Error parsing start date: {}", e.getMessage());
            }
        } else if (searchCriteria.getEndDate() != null && !searchCriteria.getEndDate().isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
                Date endDate = sdf.parse(searchCriteria.getEndDate());
                
                spec = spec.and((root, query, cb) -> 
                    cb.lessThanOrEqualTo(root.get("dateSubmitted"), endDate));
            } catch (Exception e) {
                log.error("Error parsing end date: {}", e.getMessage());
            }
        }

        Page<NCBSCApplication> applications = applicationRepository.findAll(spec, pageable);
        return applications.map(app -> {
            Enums.Status applicationStatus = app.getApplicationStatus();

            NCBSCApplicationListDto model = new NCBSCApplicationListDto();
            model.setReferenceNumber(app.getReferenceNumber());
            model.setOrganisationId(app.getOrganisationId());
            model.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
            model.setDateSubmitted(app.getDateSubmitted());
            model.setApplicationStatus(applicationStatus);
            model.setCourseTitle(app.getShortCourseInformation().getTitle() != null ? app.getShortCourseInformation().getTitle() : "");

            model.setApplicationStatus(app.getApplicationStatus());
            model.setApplicationState(app.getApplicationState());

            if(app.getApplicationState().equals(Enums.State.IN_APPROVAL) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedManager());
            }else if(app.getApplicationState().equals(Enums.State.IN_REVIEW) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedOfficer());
            }else if(app.getApplicationState().equals(Enums.State.IN_PROCESSING) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgent());
            }else if(app.getApplicationState().equals(Enums.State.SUBMITTED) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgentLead());
            }else{
                model.setAssignedTo(null);
            }

            ApiResponse<?> apiResponse = companyClient.fetchCompanyById(app.getOrganisationId());
            if (apiResponse.isStatus() && apiResponse.getData() instanceof CompanyDTO res && !isEmpty(res.getName())) {
                model.setCompanyName(res.getName());
            }
            return model;
        });
    }

    public @NonNull Optional<NCBSCApplication> getApplicationById(String id) {
        return applicationRepository.findByUuid(id);
    }

    @Transactional
    public RecognitionUpdateDto updateApplication(String id, RecognitionUpdateDto dto) {

        NCBSCApplication application = getApplicationById(id)
                .orElseThrow(() -> new NoSuchElementException("Application not found with ID: " + id));

        NCBSCApplication updatedApplication = getRecognition(dto, Optional.of(application));
        applicationRepository.save(updatedApplication);

        updateCourseDeliverySchedules(id, dto.getCourseDeliverySchedule());

        ShortCourseInformation info = dto.getShortCourseInformation();
        if (info != null) {
            info.setApplication(application);
            updateShortCourseInformation(info);
        }

        updateCourseContentAndDelivery(id, updatedApplication, dto, null);

        updateScopeOfAccreditation(id, dto.getScopeOfAccreditation());

        return dto;
    }

    private static NCBSCApplication getRecognition(RecognitionUpdateDto dto, Optional<NCBSCApplication> existingApplicationOpt) {
        NCBSCApplication recognition = existingApplicationOpt.get();

        recognition.setUpdatedAt(dto.getUpdatedAt());
        recognition.setTrainingNeedsAssessmentPurpose(dto.getTrainingNeedsAssessmentPurpose());
        recognition.setTrainingNeedsAssessmentSkillsNeedsAnalysis(dto.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
        recognition.setShortCourseDeliveryMode(dto.getShortCourseDeliveryMode());
        recognition.setKeyFacilitation(dto.getKeyFacilitation());
        recognition.setAssessmentType(dto.getAssessmentType());
        recognition.setCertification(dto.getCertification());
        recognition.setThirdPartyArrangements(dto.getThirdPartyArrangements());
        recognition.setResources(dto.getResources());
        recognition.setShortCourseEndorsement(dto.getShortCourseEndorsement());
        recognition.setApplicationStatus(dto.getApplicationStatus());
        recognition.setApplicationState(dto.getApplicationState());
        return recognition;
    }

    @Transactional
    public void deleteApplication(String id) {
        applicationRepository.deleteById(id);
    }

    public List<Map<String, Object>> getLearningOutcomes(String courseContentAndDelivery) {
        return learningOutcomeRepo.findByCourseContentId(courseContentAndDelivery).stream()
                .map(outcome -> {
                    Map<String, Object> outcomeMap = new HashMap<>();
                    outcomeMap.put("id", outcome.getId());
                    outcomeMap.put("uuid", outcome.getUuid());
                    outcomeMap.put("outcome", outcome.getOutcome());
                    List<AssessmentCriteria> assessmentCriteria = getAssessmentCriteria(outcome.getUuid());
                    if(!assessmentCriteria.isEmpty()){
                        outcomeMap.put("assessmentCriteria", assessmentCriteria);
                    }
                    return outcomeMap;
                })
                .toList();
    }

    public List<AssessmentCriteria> getAssessmentCriteria(String learningOutcome) {
        return assessmentCriteriaRepo.findByLearningOutcomeId(learningOutcome);
    }

    public List<ScopeOfAccreditation> getScopeOfAccreditation(String applicationId) {
        return scopeOfAccreditationRepository.findByApplicationId(applicationId);
    }

    public List<CourseDeliverySchedule> getCourseDeliverySchedule(String applicationId) {
        return courseDeliveryScheduleRepo.findByApplicationId(applicationId);
    }

    public @NonNull Optional<NCBSCApplication> getApplicationByReference(String referenceNumber) {
        return applicationRepository.findByReferenceNumber(referenceNumber);
    }

    public @NonNull Optional<NCBSCApplication> getApplicationByApplicationNumber(String applicationNumber) {
        return applicationRepository.findByApplicationNumber(applicationNumber);
    }
    public int updateApplicationAssignedUser(String referenceNumber, Enums.UserRoles role, String userId) {
        return applicationRepository.updateApplicationAssignedUser(referenceNumber, role.name(), userId);
    }
    @Transactional
    public int updateApplicationStatus(String applicationReference, String role, String action) {
        return applicationRepository.changeApplicationStatus(applicationReference, role, action);
    }

    public String sanitizeHtml(String html) {
        return Jsoup.clean(html, Whitelist.relaxed());
    }

    public Page<NCBSCApplicationListDto> getAllCompanyApplications(String companyId, PageRequest pageable) {
        Page<NCBSCApplication> applications = applicationRepository.findByCompanyId(companyId, pageable);
        return applications.map(app -> {
            Enums.Status applicationStatus = app.getApplicationStatus();

            NCBSCApplicationListDto model = new NCBSCApplicationListDto();
            model.setReferenceNumber(app.getReferenceNumber());
            model.setApplicationNumber(app.getApplicationNumber());
            model.setOrganisationId(app.getOrganisationId());
            model.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
            model.setDateSubmitted(app.getDateSubmitted());
            model.setApplicationStatus(applicationStatus);
            model.setCourseTitle(app.getShortCourseInformation().getTitle() != null ? app.getShortCourseInformation().getTitle() : "");

            model.setApplicationStatus(app.getApplicationStatus());
            model.setApplicationState(app.getApplicationState());

            if(app.getApplicationState().equals(Enums.State.IN_APPROVAL) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedManager());
            }else if(app.getApplicationState().equals(Enums.State.IN_REVIEW) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedOfficer());
            }else if(app.getApplicationState().equals(Enums.State.IN_PROCESSING) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgent());
            }else if(app.getApplicationState().equals(Enums.State.SUBMITTED) && !app.getApplicationStatus().equals(Enums.Status.CHANGE_REQUEST)){
                model.setAssignedTo(app.getAssignedAgentLead());
            }else{
                model.setAssignedTo(null);
            }

            //TODO Fetch assigned user name

            return model;
        });
    }

    public void updateCourseDeliverySchedules(String applicationId, Set<CourseDeliverySchedule> schedules) {
        String insertOrUpdateSql = """
        INSERT INTO course_delivery_schedule (id, uuid, application_id, date, topic, hours)
        VALUES (?, ?, ?, ?, ?, ?)
        ON CONFLICT (uuid) DO UPDATE SET
            date = EXCLUDED.date,
            topic = EXCLUDED.topic,
            hours = EXCLUDED.hours
        """;

        for (CourseDeliverySchedule schedule : schedules) {
            jdbcTemplate.update(insertOrUpdateSql,
                    defaultId(schedule.getId()),
                    defaultUuid(schedule.getUuid()),
                    applicationId,
                    schedule.getDate(),
                    schedule.getTopic(),
                    schedule.getHours());
        }
    }

    public void updateShortCourseInformation(ShortCourseInformation sci) {
        String sql = """
                INSERT INTO short_course_information (
                    id, uuid, title, type, duration, field_of_learning, sub_field_of_learning, level,
                    accrediting_body, course_learning_time, start_date, end_date, year_due_for_review,
                    target_population, entry_requirements, application_id
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
                ON CONFLICT (uuid) DO UPDATE SET
                    title = EXCLUDED.title,
                    type = EXCLUDED.type,
                    duration = EXCLUDED.duration,
                    field_of_learning = EXCLUDED.field_of_learning,
                    sub_field_of_learning = EXCLUDED.sub_field_of_learning,
                    level = EXCLUDED.level,
                    accrediting_body = EXCLUDED.accrediting_body,
                    course_learning_time = EXCLUDED.course_learning_time,
                    start_date = EXCLUDED.start_date,
                    end_date = EXCLUDED.end_date,
                    year_due_for_review = EXCLUDED.year_due_for_review,
                    target_population = EXCLUDED.target_population,
                    entry_requirements = EXCLUDED.entry_requirements;
                """;
        sci.setId(defaultId(sci.getId()));
        sci.setUuid(defaultUuid(sci.getUuid()));
        jdbcTemplate.update(sql,
                sci.getId(), sci.getUuid(), sci.getTitle(), sci.getType(), sci.getDuration(),
                sci.getFieldOfLearning(), sci.getSubFieldOfLearning(), sci.getLevel(),
                sci.getAccreditingBody(), sci.getCourseLearningTime(), sci.getStartDate(),
                sci.getEndDate(), sci.getYearDueForReview(), sci.getTargetPopulation(),
                sci.getEntryRequirements(), sci.getApplication().getUuid()
        );
    }

    public void updateCourseContentAndDelivery(String applicationId, NCBSCApplication application, @Nullable RecognitionUpdateDto dto, @Nullable NOCApplication nocApplication) {
        CourseContentDelivery content = new CourseContentDelivery();
        if (dto != null && dto.getCourseContentAndDelivery() != null){
            content = dto.getCourseContentAndDelivery();
        }

        content.setApplication(application);
        String uuid = defaultUuid(content.getUuid());

        String sql = "INSERT INTO course_content_delivery (id, uuid, application_id, exit_level_outcomes, " +
                "learning_outcomes_summary, short_course_delivery_mode, short_course_delivery_type, location) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?) " +
                "ON CONFLICT (uuid) DO UPDATE SET " +
                "exit_level_outcomes = EXCLUDED.exit_level_outcomes, " +
                "learning_outcomes_summary = EXCLUDED.learning_outcomes_summary, " +
                "short_course_delivery_mode = EXCLUDED.short_course_delivery_mode, " +
                "short_course_delivery_type = EXCLUDED.short_course_delivery_type, " +
                "location = EXCLUDED.location";

        jdbcTemplate.update(sql,
                defaultId(content.getId()),
                uuid,
                applicationId,
                content.getExitLevelOutcomes(),
                content.getLearningOutcomesSummary(),
                content.getShortCourseDeliveryMode(),
                content.getShortCourseDeliveryType(),
                content.getLocation());

        assert dto != null;
        if (dto.getLearningOutcomes() != null) {
            Optional<CourseContentDelivery> contentDelivery = courseContentDeliveryRepo.findByUuid(uuid);
            contentDelivery.ifPresent(delivery -> dto.getLearningOutcomes().forEach(lo -> upsertLearningOutcome(lo, uuid)));
        }
    }

    public void upsertLearningOutcome(LearningOutcomePayloadDto payload, String courseContentUuid) {
        LearningOutcome outcome = new LearningOutcome();
        outcome.setId(defaultId(payload.getId()));
        outcome.setUuid(defaultUuid(payload.getUuid()));
        outcome.setOutcome(payload.getOutcome());

        learningOutcomeRepo.upsertLearningOutcome(
                outcome.getId(), outcome.getUuid(), outcome.getOutcome(), courseContentUuid);

        learningOutcomeRepo.findByUuid(outcome.getUuid()).ifPresent(saved -> {
            if (payload.getAssessmentCriteria() != null) {
                payload.getAssessmentCriteria().stream().map(ac -> {
                    AssessmentCriteria criteria = new AssessmentCriteria();
                    criteria.setId(defaultId(ac.getId()));
                    criteria.setUuid(defaultUuid(ac.getUuid()));
                    criteria.setTopic(ac.getTopic());
                    criteria.setObjective(ac.getObjective());
                    criteria.setDeliveryStrategy(ac.getDeliveryStrategy());
                    criteria.setAssessmentStrategy(ac.getAssessmentStrategy());
                    return criteria;
                }).forEach(criteria -> assessmentCriteriaRepo.upsertCriteria(
                        criteria.getId(), criteria.getUuid(), criteria.getTopic(), criteria.getObjective(),
                        criteria.getDeliveryStrategy(), criteria.getAssessmentStrategy(), saved.getUuid()));
            }
        });
    }

    public void updateScopeOfAccreditation(String applicationId, Set<ScopeOfAccreditation> accreditations) {
        if (accreditations == null) return;

        String sql = "INSERT INTO scope_of_accreditation (id, uuid, application_id, fields_of_learning_accredited) " +
                "VALUES (?, ?, ?, ?) ON CONFLICT (uuid) DO UPDATE SET fields_of_learning_accredited = EXCLUDED.fields_of_learning_accredited";

        for (ScopeOfAccreditation accreditation : accreditations) {
            jdbcTemplate.update(sql,
                    defaultId(accreditation.getId()),
                    defaultUuid(accreditation.getUuid()),
                    applicationId,
                    accreditation.getFieldsOfLearningAccredited());
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public BatchStatusUpdateResult batchUpdateApplicationStatus(
            List<String> referenceNumbers, 
            String role, 
            String action, 
            String userId,
            String comments) {
        
        BatchStatusUpdateResult result = new BatchStatusUpdateResult();
        List<BatchStatusUpdateResult.ApplicationUpdateResult> updateResults = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // Create a new batch record
        Batch batch = new Batch();
        batch.setBatchType("ncbsc");
        batch.setBatchStatus(ApplicationStatus.PROCESSING);
        batch.setApplicationStatus(ApplicationStatus.valueOf(action));
        batch.setApplicationCount(referenceNumbers.size());
        batch.setActionTakenBy(role); 
        batch.setUserId(UUID.fromString(userId));
        batch.setDatePosted(new Date());
        
        // Save the batch to get an ID
        batch = batchRepository.save(batch);
        UUID batchId = batch.getId();
        
        for (String referenceNumber : referenceNumbers) {
            try {
                Optional<NCBSCApplication> applicationOpt = getApplicationByReference(referenceNumber);
                
                if (applicationOpt.isPresent()) {
                    NCBSCApplication application = applicationOpt.get();
                    
                    // Check if application is already in REJECTED status
                    if (Enums.Status.REJECTED.equals(application.getApplicationStatus())) {
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                referenceNumber, false, "Application is already rejected and cannot be updated"));
                        failureCount++;
                        continue;
                    }
                    
                    // Check if application is already in CHANGE_REQUEST status
                    if (Enums.Status.CHANGE_REQUEST.equals(application.getApplicationStatus())) {
                         updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                referenceNumber, false, "Application is already in change request status and cannot be approved"));
                         failureCount++;
                         continue;
                                    }
                    
                    int updateResult = updateApplicationStatus(referenceNumber, role, action);
                    
                    if (updateResult > 0) {
                        // Create a new instance of NCBSCApplicationComments
                        NCBSCApplicationComments logEntry = new NCBSCApplicationComments();
                        logEntry.setNcbscApplication(application);
                        logEntry.setAction(action);
                        logEntry.setComments(sanitizeHtml(comments));
                        logEntry.setUpdatedBy(userId);
                        logEntry.setTimestamp(LocalDateTime.now());
                        logEntry.setBatchId(batchId);
                        
                        // Save the comment
                        commentsService.createComments(logEntry);
                        
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                referenceNumber, true, "Status updated successfully"));
                        successCount++;
                    } else {
                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                referenceNumber, false, "Failed to update application status"));
                        failureCount++;
                    }
                } else {
                    updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                            referenceNumber, false, "Application not found"));
                    failureCount++;
                }
            } catch (Exception e) {
                updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                        referenceNumber, false, "Error: " + e.getMessage()));
                failureCount++;
            }
        }
        
        result.setTotalProcessed(referenceNumbers.size());
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setResults(updateResults);
        
        // Update the batch with the results
        ApplicationStatus finalStatus = (failureCount == 0) ? 
                ApplicationStatus.PROCESSED : ApplicationStatus.PARTIALLY_PROCESSED;
        batch.setBatchStatus(finalStatus); // Updated from setApplicationStatus
        
        // Store the full response in batch_logs
        Map<String, Object> batchLogs = new HashMap<>();
        batchLogs.put("timestamp", new Date());
        batchLogs.put("action", action);
        batchLogs.put("role", role);
        batchLogs.put("result", result);
        batch.setBatchLogs(batchLogs);
        
        // Update the batch record
        batchRepository.save(batch);
        
        return result;
    }

      /**
     * Updates the process instance ID for an application with the given reference number
     * 
     * @param referenceNumber the reference number of the application
     * @param processInstanceId the process instance ID to save
     * @return true if the update was successful, false otherwise
     */
    @Transactional
    public boolean updateProcessInstanceId(String referenceNumber, String processInstanceId) {
        Optional<NCBSCApplication> applicationOpt = getApplicationByReference(referenceNumber);
        if (applicationOpt.isPresent()) {
            NCBSCApplication application = applicationOpt.get();
            application.setProcessInstanceId(processInstanceId);
            applicationRepository.save(application);
            return true;
        }
        return false;
    }

    public int upgradeApplication(String applicationId, String referenceNumber, String applicationState, String applicationStatus) {
        return applicationRepository.upgradeApplication(applicationId, referenceNumber, applicationState, applicationStatus);
    }

    public Map<String, Object> getApplicationsByFilters(
            String role, 
            String userId, 
            UUID companyId, 
            String applicationStatus, 
            String applicationState, 
            String trainingProvider,
            Date startDate, 
            Date endDate, 
            String referenceNumber, 
            String courseTitle, 
            String search, 
            int pageNumber, 
            int size) {
        
        Specification<NCBSCApplication> spec = Specification.where(BaseSpecifications.isNotDeleted());
        
                
        if (companyId != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("organisationId"), companyId));
        }
        
        if (applicationStatus != null && !applicationStatus.isEmpty()) {
            try {
                Enums.Status status = Enums.Status.valueOf(applicationStatus);
                spec = spec.and((root, query, cb) -> cb.equal(root.get("applicationStatus"), status));
            } catch (IllegalArgumentException e) {
                // Invalid status, ignore this filter
            }
        }
        
        if (applicationState != null && !applicationState.isEmpty()) {
            try {
                Enums.State state = Enums.State.valueOf(applicationState);
                spec = spec.and((root, query, cb) -> cb.equal(root.get("applicationState"), state));
            } catch (IllegalArgumentException e) {
                // Invalid state, ignore this filter
            }
        }
        
        // Add remaining filters
        if (referenceNumber != null && !referenceNumber.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("referenceNumber")), 
                    "%" + referenceNumber.toLowerCase() + "%"));
        }
        
        if (courseTitle != null && !courseTitle.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                Join<NCBSCApplication, ?> courseInfoJoin = root.join("shortCourseInformation", JoinType.LEFT);
                return cb.like(cb.lower(courseInfoJoin.get("title")), 
                        "%" + courseTitle.toLowerCase() + "%");
            });
        }
        
        if (trainingProvider != null && !trainingProvider.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                Join<NCBSCApplication, ?> providerJoin = root.join("trainingProvider", JoinType.LEFT);
                return cb.like(cb.lower(providerJoin.get("name")), 
                        "%" + trainingProvider.toLowerCase() + "%");
            });
        }
        
        // Date range filter
        if (startDate != null && endDate != null) {
            spec = spec.and((root, query, cb) -> 
                cb.between(root.get("dateSubmitted"), startDate, endDate));
        } else if (startDate != null) {
            spec = spec.and((root, query, cb) -> 
                cb.greaterThanOrEqualTo(root.get("dateSubmitted"), startDate));
        } else if (endDate != null) {
            spec = spec.and((root, query, cb) -> 
                cb.lessThanOrEqualTo(root.get("dateSubmitted"), endDate));
        }
        
        // General search across multiple fields
        if (search != null && !search.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                String searchLike = "%" + search.toLowerCase() + "%";
                
                // Create subquery for course title search
                Subquery<String> courseSubquery = query.subquery(String.class);
                Root<NCBSCApplication> courseRoot = courseSubquery.correlate(root);
                Join<NCBSCApplication, ?> courseInfoJoin = courseRoot.join("shortCourseInformation", JoinType.LEFT);
                courseSubquery.select(courseInfoJoin.get("title"))
                    .where(cb.like(cb.lower(courseInfoJoin.get("title")), searchLike));
                
                // Create subquery for organization name search
                Subquery<String> orgSubquery = query.subquery(String.class);
                Root<NCBSCApplication> orgRoot = orgSubquery.correlate(root);
                orgSubquery.select(orgRoot.get("organisationId"))
                    .where(cb.like(cb.lower(orgRoot.get("organisationId")), searchLike));
                
                return cb.or(
                    cb.like(cb.lower(root.get("referenceNumber")), searchLike),
                    cb.like(cb.lower(root.get("applicationNumber")), searchLike),
                    cb.exists(courseSubquery),
                    cb.exists(orgSubquery),
                    // Add status search if the search term matches a status value
                    cb.like(cb.lower(root.get("applicationStatus").as(String.class)), searchLike)
                );
            });
        }
        
        // Default sort by submission date, newest first
        Sort sort = Sort.by(Sort.Direction.DESC, "dateSubmitted");
        
        PageRequest pageable = PageRequest.of(pageNumber, size, sort);
        Page<NCBSCApplication> applications = applicationRepository.findAll(spec, pageable);
        
        List<NCBSCApplicationListDto> applicationDtos = applications.map(app -> {
            NCBSCApplicationListDto dto = new NCBSCApplicationListDto();
            // Map entity to DTO
            dto.setReferenceNumber(app.getReferenceNumber());
            dto.setApplicationNumber(app.getApplicationNumber());
            dto.setOrganisationId(app.getOrganisationId());
            dto.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
            dto.setDateSubmitted(app.getDateSubmitted());
            dto.setApplicationStatus(app.getApplicationStatus());
            dto.setApplicationState(app.getApplicationState());
            // Map other fields
            return dto;
        }).getContent();
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", applications.getNumber());
        metadata.put("totalPages", applications.getTotalPages());
        metadata.put("totalElements", applications.getTotalElements());
        metadata.put("pageSize", applications.getSize());
        
        Map<String, Object> response = new HashMap<>();
        response.put("metadata", metadata);
        response.put("results", applicationDtos);
        
        return response;
    }
}
