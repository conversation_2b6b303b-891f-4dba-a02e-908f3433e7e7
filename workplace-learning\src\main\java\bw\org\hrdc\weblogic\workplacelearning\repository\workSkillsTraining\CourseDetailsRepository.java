package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CourseDetailsRepository extends JpaRepository<CourseDetails, UUID> {

    List<CourseDetails> findByApplicationId(UUID applicationId);

    Optional<CourseDetails> findById(UUID id);
}