package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.util.ApiResponse;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.UUID;

@Component
public class WorkplaceLearningClientFallback implements WorkplaceLearningClient {
    
    @Override
    public ApiResponse<?> getApplicationByReferenceNumber(String referenceNumber) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getApplicationById(String id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getApplicationById(UUID id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> searchByReferenceNumber(String applicationNumber) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getComplaintById(String id) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> updateComplaintStatus(String id, Map<String, Object> statusUpdate) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable - cannot update complaint status",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> escalateComplaint(String id, Map<String, Object> escalationData) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable - cannot escalate complaint",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> addComplaintComment(String id, Map<String, Object> comment) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable - cannot add comment",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> assignComplaint(String id, Map<String, Object> assignmentData) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable - cannot assign complaint",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> closeComplaint(String id, Map<String, Object> closureData) {
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable - cannot close complaint",
            null,
            null
        );
    }
}