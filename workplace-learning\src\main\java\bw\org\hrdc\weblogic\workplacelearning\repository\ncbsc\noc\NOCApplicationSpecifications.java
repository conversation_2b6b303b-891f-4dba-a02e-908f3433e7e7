package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc;


import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import org.springframework.data.jpa.domain.Specification;

public class NOCApplicationSpecifications {

    public static Specification<NOCApplication> byOrganisationId(String orgId) {
        return (root, query, cb) -> cb.equal(root.get("organisationId"), orgId);
    }

    public static Specification<NOCApplication> isNotDeleted() {
        return (root, query, cb) -> cb.isFalse(root.get("isDeleted"));
    }

    public static Specification<NOCApplication> byState(String state) {
        return (root, query, cb) -> state == null || state.isEmpty()
                ? cb.conjunction()
                : cb.equal(root.get("applicationState"), state);
    }

    public static Specification<NOCApplication> byAssignedTo(String assignedTo) {
        return (root, query, cb) -> cb.or(
                cb.equal(root.get("assignedAgent"), assignedTo),
                cb.equal(root.get("assignedAgentLead"), assignedTo),
                cb.equal(root.get("assignedOfficer"), assignedTo),
                cb.equal(root.get("assignedOfficerLead"), assignedTo),
                cb.equal(root.get("assignedManager"), assignedTo)
        );
    }

    public static Specification<NOCApplication> byReferenceNumber(String referenceNumber) {
        return (root, query, cb) -> cb.equal(cb.lower(root.get("referenceNumber")), referenceNumber.toLowerCase());
    }

}
