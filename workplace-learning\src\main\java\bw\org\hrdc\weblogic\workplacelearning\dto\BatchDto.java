package bw.org.hrdc.weblogic.workplacelearning.dto;

import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

/**
 * DTO for managing Batch details.
 */
@Data
@Schema(
        name = "Batch",
        description = "Schema to hold Batch details"
)
public class BatchDto {

    @Schema(description = "Unique identifier for the batch", example = "d4b9f10b-58cc-4372-a567-0e02b2c3d234")
    private UUID id;

    @NotNull(message = "Date created cannot be null")
    @Schema(description = "Date the batch was created", example = "2024-11-26T10:00:00Z")
    private Date datePosted;

    @Schema(description = "Status of the batch processing", example = "PROCESSED")
    private ApplicationStatus batchStatus;

    @Schema(description = "Status of the applications in the batch", example = "APPROVED")
    private ApplicationStatus applicationStatus;

    @NotNull(message = "Application count cannot be null")
    @Schema(description = "Number of applications in the batch", example = "25")
    private int applicationCount;

    @NotNull(message = "Assigned user cannot be null")
    @Schema(description = "Name of the user who took the action", example = "John Doe")
    private String actionTakenBy;

    @NotNull(message = "User ID cannot be null")
    @Schema(description = "The ID of the user assigned to the batch", example = "e5f6g70b-58cc-4372-a567-0e02b2c3d234")
    private UUID userId;
}
