package bw.org.hrdf.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Required Document DTO")
public class RequiredDocumentDTO {
    @Schema(description = "Type of document")
    private String documentType;
    
    @Schema(description = "File path")
    private String filePath;
}