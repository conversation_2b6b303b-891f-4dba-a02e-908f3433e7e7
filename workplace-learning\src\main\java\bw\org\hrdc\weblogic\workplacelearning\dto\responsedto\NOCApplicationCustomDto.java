package bw.org.hrdc.weblogic.workplacelearning.dto.responsedto;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.CourseContentDeliveryJson;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class NOCApplicationCustomDto {
    private String assignedAgent;
    private String assignedAgentLead;
    private String assignedOfficerLead;
    private String assignedOfficer;
    private String assignedManager;
    private String referenceNumber;
    private String applicationNumber;
    private String justification;
    private String recognitionNumber;
    private String applicationState;
    private String organisationId;
    private String trainingNeedsAssessmentPurpose;
    private String trainingNeedsAssessmentSkillsNeedsAnalysis;
    private String shortCourseDeliveryMode;
    private String keyFacilitation;
    private String assessmentType;
    private String certification;
    private String thirdPartyArrangements;
    private String resources;
    private String shortCourseEndorsement;
    private LocalDateTime dateSubmitted;
    private String applicationStatus;
    private ShortCourseInformation shortCourseInformation;
    private CourseContentDeliveryJson courseContentAndDelivery;
    private Long id;
    private String uuid;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private boolean deleted;
    private String processInstanceId;

    // Add getters and setters (or use Lombok @Data)
}
