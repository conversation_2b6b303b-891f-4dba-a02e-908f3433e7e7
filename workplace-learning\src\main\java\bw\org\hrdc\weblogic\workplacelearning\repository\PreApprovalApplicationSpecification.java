package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.UUID;

/**
 * Specification for PreApprovalApplication entity.
 */
public class PreApprovalApplicationSpecification {

    /**
     * Creates a specification for filtering PreApprovalApplication by various criteria.
     *
     * @param organisationId    the ID of the organization.
     * @param userId            the ID of the user who submitted the application.
     * @param status            the status of the application (e.g., NEW, APPROVED, REJECTED).
     * @param trainingProvider  the name of the training provider.
     * @param startDate         the start date for filtering applications.
     * @param endDate           the end date for filtering applications.
     * @param referenceNumber   the reference number for filtering applications.
     * @return a specification for filtering PreApprovalApplication.
     */
    public static Specification<PreApprovalApplication> searchByCriteria(
            UUID organisationId,
            UUID userId,
            String status,
            String trainingProvider,
            java.time.LocalDateTime startDate,
            java.time.LocalDateTime endDate,
            String referenceNumber
    ) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            // Add condition to exclude soft-deleted applications
            p = cb.and(p, cb.isFalse(root.get("deleted")));

            if (organisationId != null) {
                p = cb.and(p, cb.equal(root.get("organisationId"), organisationId));
            }

            if (userId != null) {
                p = cb.and(p, cb.equal(root.get("userId"), userId));
            }

            if (status != null && !status.isEmpty()) {
                p = cb.and(p, cb.equal(root.get("status"), status));
            }

            if (trainingProvider != null && !trainingProvider.isEmpty()) {
                p = cb.and(p, cb.like(cb.lower(root.get("trainingProvider")), "%" + trainingProvider.toLowerCase() + "%"));
            }

            if (startDate != null && endDate != null) {
                p = cb.and(p, cb.between(root.get("createdAt"), startDate, endDate));
            } else if (startDate != null) {
                p = cb.and(p, cb.greaterThanOrEqualTo(root.get("createdAt"), startDate));
            } else if (endDate != null) {
                p = cb.and(p, cb.lessThanOrEqualTo(root.get("createdAt"), endDate));
            }

            if (referenceNumber != null && !referenceNumber.isEmpty()) {
                // Use partial matching with case insensitive comparison
                String refSearchTerm = "%" + referenceNumber.toLowerCase() + "%";
                p = cb.and(p, cb.like(cb.lower(root.get("referenceNumber")), refSearchTerm));
            }
            return p;
        };
    }

    /**
     * Creates a specification for filtering PreApprovalApplication by role and user ID.
     * For AGENT_LEAD and OFFICER_LEAD roles, returns all non-deleted applications.
     * For AGENT, OFFICER, and MANAGER roles, returns applications where the user is assigned.
     *
     * @param role the role of the user
     * @param userId the ID of the user
     * @return a specification for filtering PreApprovalApplication
     */
    public static Specification<PreApprovalApplication> searchByRoleAndUserId(String role, String userId) {
        return (root, query, cb) -> {
            Predicate p = cb.conjunction();

            // Add condition to exclude soft-deleted applications
            p = cb.and(p, cb.isFalse(root.get("deleted")));

            // Add role-based filtering
            switch (role) {
                case "AGENT_LEAD":
                case "OFFICER_LEAD":
                    // No additional filtering needed for leads
                    break;
                case "AGENT":
                    p = cb.and(p, cb.equal(root.get("assignedAgent"), userId));
                    break;
                case "OFFICER":
                    p = cb.and(p, cb.equal(root.get("assignedOfficer"), userId));
                    break;
                case "MANAGER":
                    p = cb.and(p, cb.equal(root.get("assignedManager"), userId));
                    break;
                default:
                    // For unknown roles, return no results
                    p = cb.and(p, cb.equal(root.get("id"), null));
            }

            // Add ordering by created date
            query.orderBy(cb.desc(root.get("createdDate")));

            return p;
        };
    }
}
