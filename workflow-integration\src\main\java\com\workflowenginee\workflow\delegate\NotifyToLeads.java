package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

@Component("notifyToLeads")
public class NotifyToLeads implements JavaDelegate {

    private final NotificationComplaintService notificationComplaintService;

    public NotifyToLeads(NotificationComplaintService notificationComplaintService) {
        this.notificationComplaintService = notificationComplaintService;
    }
    
    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String role = (String) execution.getVariable("role");
        String companyId = (String) execution.getVariable("complaintId");
        String applicationType = Enums.ApplicationType.COMPLAINTS.name();

        System.out.println("[Process: " + processInstanceId + "] Starting email notification process");
        System.out.println("companyId: " + companyId);

        Map<String, Object> applicationData;
        try {
            applicationData = (Map<String, Object>) execution.getVariable("applicationData");
            System.out.println("11111111111111 : " + applicationData);

        } catch (ClassCastException e) {
            System.err.println("[Process: " + processInstanceId + "] Error casting ApplicationData to Map: " + e.getMessage());
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "Invalid application data format");
            return;
        }

        if (applicationData == null ) {
            System.err.println("[Process: " + processInstanceId + "] No application data found to send email.");
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "No application data found");
            return;
        }


        try {
            String applicationId = String.valueOf(applicationData.get("uuid"));
            String referenceNumber = String.valueOf(applicationData.get("referenceNumber"));
            String applicationStatus = String.valueOf(applicationData.get("status"));
            String applicationState = String.valueOf(applicationData.get("state"));

            System.out.println("[Process: " + processInstanceId + "] Preparing email for application: " + applicationId);
            System.out.println("[Process: " + processInstanceId + "] Reference Number: " + referenceNumber);
            System.out.println("[Process: " + processInstanceId + "] Application Status: " + applicationStatus);

            String emailSubject = "NCBSC Application " + referenceNumber + " - Status: " + applicationStatus;
            String emailBody = "Application ID: " + applicationId + "\n"
                            + "Reference Number: " + referenceNumber + "\n"
                            + "Status: " + applicationStatus + "\n\n"
                            + "This is an automated notification about the application's status update.";

            System.out.println("[Process: " + processInstanceId + "] Email Subject: " + emailSubject);
            System.out.println("[Process: " + processInstanceId + "] Email Body:\n" + emailBody);

            // Store in process variables
            execution.setVariable("emailSubject", emailSubject);
            execution.setVariable("emailBody", emailBody);
            execution.setVariable("emailSent", true);

             // Create shared DTO
            NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .companyId(companyId)
                .applicationState(applicationState)
                .applicationType(applicationType)
                .build();
                
            // Notification logic
            String currentActivityId = execution.getCurrentActivityId();

            if (Enums.Role.AGENT.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(Enums.Role.OFFICER_LEAD.name())
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .applicationType(applicationType)
                .build(); 

                notificationComplaintService.notificationToLeads(notifyUsersByRoleDto);


            } else if (Enums.Role.AGENT_LEAD.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                .role(Enums.Role.AGENT_LEAD.name())
                .referenceNumber(referenceNumber)
                .applicationId(applicationId)
                .applicationStatus(applicationStatus)
                .applicationType(applicationType)
                .build(); 
                notificationComplaintService.notificationToLeads(notifyUsersByRoleDto);

            }
            

            System.out.println("[Process: " + processInstanceId + "] Notification flow completed.");

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error during email or notification processing: " + e.getMessage());
            e.printStackTrace();
            // execution.setVariable("emailSent", false);
            // execution.setVariable("emailError", e.getMessage());
        }
    }
    


}
