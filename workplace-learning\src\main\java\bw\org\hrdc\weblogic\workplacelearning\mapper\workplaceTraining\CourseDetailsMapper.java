package bw.org.hrdc.weblogic.workplacelearning.mapper.workplaceTraining;


import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.CourseDetailsDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.UUID;
import org.slf4j.Logger;

@Component
public class CourseDetailsMapper {

    public static CourseDetailsDto toDto(CourseDetails entity) {
        if (entity == null) {
            return null;
        }

        CourseDetailsDto dto = new CourseDetailsDto();
        dto.setId(entity.getId());
        dto.setIsNotLocalCourse(entity.getIsNotLocalCourse());
        dto.setCourseId(entity.getCourseId());
        dto.setProgramme(entity.getProgramme());
        dto.setSkills(entity.getSkills());
        dto.setTrainingStartDate(entity.getTrainingStartDate());
        dto.setTrainingEndDate(entity.getTrainingEndDate());
        dto.setInstitution(entity.getInstitution());
        dto.setLocation(entity.getLocation());
        dto.setAccreditingBody(entity.getAccreditingBody());
        dto.setLevelOfTraining(entity.getLevelOfTraining());
        dto.setCostOfTraining(entity.getCostOfTraining());
        dto.setNoncitizens(Integer.valueOf(entity.getNoncitizens()));
        dto.setCitizens(Integer.valueOf(entity.getCitizens()));
        dto.setPeopleTrained(Integer.valueOf(entity.getPeopleTrained()));

        if(entity.getHasPreApproval() != null) dto.setHasPreApproval(entity.getHasPreApproval());

        return dto;
    }

    public static CourseDetails toEntity(CourseDetailsDto dto, WorkPlaceTrainingPlan trainingPlan) {
        if (dto == null) {
            return null;
        }

        CourseDetails entity = new CourseDetails();
        entity.setId(dto.getId());
        entity.setCourseId(dto.getCourseId());
        entity.setIsNotLocalCourse(dto.getIsNotLocalCourse());
//        entity.setTrainingPlan(trainingPlan);
        entity.setProgramme(dto.getProgramme());
        entity.setSkills(dto.getSkills());
        entity.setTrainingStartDate(dto.getTrainingStartDate());
        entity.setTrainingEndDate(dto.getTrainingEndDate());
        entity.setInstitution(dto.getInstitution());
        entity.setLocation(dto.getLocation());
        entity.setAccreditingBody(dto.getAccreditingBody());
        entity.setLevelOfTraining(dto.getLevelOfTraining());
        entity.setCostOfTraining(dto.getCostOfTraining());
        entity.setNoncitizens(Integer.valueOf(dto.getNoncitizens()));
        entity.setCitizens(Integer.valueOf(dto.getCitizens()));
        entity.setPeopleTrained(Integer.valueOf(dto.getPeopleTrained()));

        if(dto.getHasPreApproval() != null) entity.setHasPreApproval(dto.getHasPreApproval());

        return entity;
    }
}