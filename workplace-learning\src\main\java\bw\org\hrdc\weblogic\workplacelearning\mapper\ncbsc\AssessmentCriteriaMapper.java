package bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.AssessmentCriteriaDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.AssessmentCriteria;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.LearningOutcome;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @CreatedOn 18/02/25 20:10
 * @UpdatedBy martinspectre
 * @UpdatedOn 18/02/25 20:10
 */

public class AssessmentCriteriaMapper {

    public static AssessmentCriteria toEntity(AssessmentCriteriaDto model, LearningOutcome learningOutcome) {
        if (model == null) {
            return null;
        }
        AssessmentCriteria assessmentCriteria = new AssessmentCriteria();

        assessmentCriteria.setTopic(model.getTopic());
        assessmentCriteria.setObjective(model.getObjective());
        assessmentCriteria.setDeliveryStrategy(model.getDeliveryStrategy());
        assessmentCriteria.setAssessmentStrategy(model.getAssessmentStrategy());
        assessmentCriteria.setLearningOutcomes(learningOutcome);
        assessmentCriteria.setDateFrom(model.getDateFrom());
        assessmentCriteria.setDateTo(model.getDateTo());

        return assessmentCriteria;
    }

    public static AssessmentCriteriaDto toDto(AssessmentCriteria entity) {
        if (entity == null) {
            return null;
        }
        AssessmentCriteriaDto model = new AssessmentCriteriaDto();
        model.setTopic(entity.getTopic());
        model.setObjective(entity.getObjective());
        model.setDeliveryStrategy(entity.getDeliveryStrategy());
        model.setAssessmentStrategy(entity.getAssessmentStrategy());
        model.setDateFrom(entity.getDateFrom());
        model.setDateTo(entity.getDateTo());
        return model;
    }

    public static Set<AssessmentCriteria> toEntityList(Set<AssessmentCriteriaDto> dtos, LearningOutcome learningOutcome) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(assessment -> AssessmentCriteriaMapper.toEntity(assessment, learningOutcome))
                .collect(Collectors.toSet());
    }
    public static Set<AssessmentCriteriaDto> toDtoList(Set<AssessmentCriteria> entities) {
        return entities.stream()
                .map(AssessmentCriteriaMapper::toDto)
                .collect(Collectors.toSet());
    }
}

