package bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import org.springframework.data.jpa.domain.Specification;

import java.util.Map;

/**
 * Custom repository interface for WorkPlaceTrainingPlan entity.
 */
public interface WorkPlaceTrainingPlanRepositoryCustom {
    
    /**
     * Get counts of training plans by their status.
     *
     * @param baseSpec the base specification to filter the training plans.
     * @return a map containing counts for different status categories.
     */
    Map<String, Long> getTrainingPlanStatusCounts(Specification<WorkPlaceTrainingPlan> baseSpec);
}