spring:
  application:
    name: discovery-server
  profiles:
    activate: default
  config:
    import: optional:configserver:${CONFIG_SERVER_URL:http://localhost:8071/}
  
# Avoid some debugging logs at startup
#logging:
#  level:
#    org:
#      springframework:
#        boot: INFO
#        web: INFO
#        netflix:
#          eureka: DEBUG
#    name: "eurekaserver"
management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: "*"
  health:
    readiness-state:
      enabled: true
    liveness-state:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
server:
  port: 8070

eureka:
  server:
    eviction-interval-timer-in-ms: 30000  # Reduce risk of premature removal
  instance:
    hostname: localhost
  client:
    fetchRegistry: true   # Allow the server to fetch services
    registerWithEureka: false  # Keep this false for Eureka Server itself
    serviceUrl:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/
