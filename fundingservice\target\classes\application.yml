server:
  port: 3461

spring:
  application:
    name: FUNDING-SERVICE
  profiles:
    active: local
  datasource:
    url: ****************************************
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: ehrdf
            client-secret: 1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd
            authorization-grant-type: authorization_code
            scope: openid,profile,email
        provider:
          keycloak:
            issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
      resourceserver:
        jwt:
          issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
          jwk-set-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc/protocol/openid-connect/certs
          audiences: account,ehrdf
  kafka:
    bootstrap-servers: localhost:9094

jwt:
  auth:
    converter:
      resource-id: ehrdf
      principal-attribute: principal_username

keycloak:
  server-url: https://hrdcdev.weblogic.co.bw/keycloak
  realm: hrdc
  client-id: ehrdf
  client-secret: 1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd
  admin-username: test_user
  admin-password: testusernew
  protocol: http

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5
    leaseExpirationDurationInSeconds: 10
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/
    healthcheck:
      enabled: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,loggers
  endpoint:
    health:
      show-details: always

feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
  circuitbreaker:
    enabled: true

logging:
  level:
    org.springframework.security: INFO
    web: INFO
    bw.org.hrdf.account: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
    bw.org.hrdf: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE