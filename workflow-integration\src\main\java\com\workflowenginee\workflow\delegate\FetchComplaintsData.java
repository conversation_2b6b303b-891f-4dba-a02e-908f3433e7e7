package com.workflowenginee.workflow.delegate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;
@Component("fetchComplaintDataDelegate")
public class FetchComplaintsData implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;
    // private final WorkplaceLearningClient workplaceLearningClient;

    public FetchComplaintsData(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        ApiResponse<?> response = null;

        try {

            response = workplaceLearningClient.getComplaintById(complaintId);

            System.out.println("response  = " + response);
            if (response != null && response.getData() instanceof Map) {
                Map<String, Object> responseData = (Map<String, Object>) response.getData();
                execution.setVariable("applicationData", responseData);
                System.out.println("[Process: " + processInstanceId + "] Set applicationData variable.");
            } else {
                System.err.println("[Process: " + processInstanceId + "] Invalid or empty response data.");
                execution.setVariable("fetchError", "Invalid or empty response data");
            }
        

        } catch (Exception ex) {
            System.err.println("[Process: " + processInstanceId + "] Unexpected error occurred:");
            ex.printStackTrace();
            execution.setVariable("fetchError", "General error during application fetch");
        }
    }

}
