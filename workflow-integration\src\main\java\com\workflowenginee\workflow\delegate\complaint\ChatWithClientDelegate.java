package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("chatWithClientDelegate")
public class ChatWithClientDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent Lead initiating chat with client for complaint: " + complaintId);

        try {
            Map<String, Object> escalatedComplaintData = (Map<String, Object>) execution.getVariable("escalatedComplaintData");
            
            if (escalatedComplaintData != null) {
                // Log the chat initiation by agent lead
                System.out.println("[Process: " + processInstanceId + "] Agent Lead is starting chat session with client");
                
                // Set chat session variables
                execution.setVariable("chatSessionActive", true);
                execution.setVariable("chatInitiatedBy", "AGENT_LEAD");
                execution.setVariable("chatReason", "ESCALATED_COMPLAINT_DISCUSSION");
                execution.setVariable("chatStartTime", System.currentTimeMillis());

                // TODO: Notify client about chat session with agent lead
                System.out.println("[Process: " + processInstanceId + "] Client would be notified about agent lead chat session");
                
                // TODO: Notify original agent about agent lead intervention
                System.out.println("[Process: " + processInstanceId + "] Agent would be notified about agent lead intervention");

                // After chat completion, mark complaint as resolved
                execution.setVariable("complaintStatus", "RESOLVED_VIA_AGENT_LEAD_CHAT");
                execution.setVariable("resolvedBy", "AGENT_LEAD");
                execution.setVariable("resolvedAt", System.currentTimeMillis());
                execution.setVariable("escalationResolved", true);

                System.out.println("[Process: " + processInstanceId + "] Agent lead chat session completed successfully");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No escalated complaint data available for chat");
                execution.setVariable("chatSessionActive", false);
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error in agent lead chat: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("chatSessionActive", false);
        }
    }
}
