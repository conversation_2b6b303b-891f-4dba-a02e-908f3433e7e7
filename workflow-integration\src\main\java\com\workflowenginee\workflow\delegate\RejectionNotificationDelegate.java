package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationService;
import com.workflowenginee.workflow.util.Enums;

@Component("rejectionNotificationDelegate")
public class RejectionNotificationDelegate implements JavaDelegate {

    private static final Logger logger = LoggerFactory.getLogger(RejectionNotificationDelegate.class);
    
    private final NotificationService notificationService;
    
    public RejectionNotificationDelegate(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        logger.info("[Process: {}] Handling rejection notification", processInstanceId);

        Map<String, Object> applicationData = null;
        try {
            applicationData = (Map<String, Object>) execution.getVariable("ApplicationData");
        } catch (ClassCastException e) {
            logger.error("[Process: {}] Error casting ApplicationData: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("rejectionNotificationSent", false);
            execution.setVariable("rejectionNotificationError", "Invalid ApplicationData format");
            return;
        }

        if (applicationData == null || !applicationData.containsKey("application")) {
            logger.error("[Process: {}] No application data found to send rejection notification", processInstanceId);
            execution.setVariable("rejectionNotificationSent", false);
            execution.setVariable("rejectionNotificationError", "No application data found");
            return;
        }

        Map<String, Object> application;
        try {
            application = (Map<String, Object>) applicationData.get("application");
        } catch (ClassCastException e) {
            logger.error("[Process: {}] Error casting application map: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("rejectionNotificationSent", false);
            execution.setVariable("rejectionNotificationError", "Invalid application structure");
            return;
        }

        try {
            // Extract required values
            String referenceNumber = String.valueOf(application.get("referenceNumber"));
            String applicationState = String.valueOf(application.get("applicationState"));
            String applicationType = (String) execution.getVariable("applicationType");
            String companyId = (String) execution.getVariable("companyId");
            String applicationStatus = Enums.Status.REJECTED.name(); // Force status to REJECTED
            String applicationId = (String) execution.getVariable("applicationId");

            String rejectionReason = (String) execution.getVariable("rejectionReason");
            if (rejectionReason == null || rejectionReason.isBlank()) {
                rejectionReason = "Application does not meet the required criteria";
            }

            logger.info("[Process: {}] Sending rejection notification for application: {}", processInstanceId, applicationId);
            logger.info("[Process: {}] Reference Number: {}", processInstanceId, referenceNumber);

            // Update process variables
            execution.setVariable("applicationStatus", applicationStatus);
            execution.setVariable("rejectionReason", rejectionReason);
            execution.setVariable("rejectionNotificationSent", true);


            try {
                if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType) || Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {

                    // Send notification
                    NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                    .referenceNumber(applicationId)
                    .applicationId(applicationId)
                    .applicationStatus(applicationStatus)
                    .companyId(companyId)
                    .applicationState(applicationState)
                    .applicationType(applicationType)
                    .build();
                    notificationService.notifyToClient(
                            notifyclientDto
                    );
                } else {
                    // Send notification
                    NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                    .referenceNumber(referenceNumber)
                    .applicationId(applicationId)
                    .applicationStatus(applicationStatus)
                    .companyId(companyId)
                    .applicationState(applicationState)
                    .applicationType(applicationType)
                    .build();
                    notificationService.notifyToClient(
                            notifyclientDto
                    );
                }
               
                logger.info("[Process: {}] Rejection notification sent successfully", processInstanceId);
            } catch (Exception notifyEx) {
                logger.error("[Process: {}] Error while notifying client: {}", processInstanceId, notifyEx.getMessage(), notifyEx);
                execution.setVariable("rejectionNotificationSent", false);
                execution.setVariable("rejectionNotificationError", "Notification service failed");
            }

        } catch (Exception e) {
            logger.error("[Process: {}] Unexpected error in rejection notification logic: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("rejectionNotificationSent", false);
            execution.setVariable("rejectionNotificationError", "Unexpected error: " + e.getMessage());
        }
    }

}