package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("agentHandleComplaintDelegate")
public class AgentHandleComplaintDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String assignee = (String) execution.getVariable("assignee");
            String agentDecision = (String) execution.getVariable("agentDecision");
            String agentComments = (String) execution.getVariable("agentComments");
            
            log.info("Agent {} handling complaint ID: {} with decision: {}", assignee, complaintId, agentDecision);

            // Prepare update payload
            Map<String, Object> updatePayload = new HashMap<>();
            updatePayload.put("complaintId", complaintId);
            updatePayload.put("action", "AGENT_ACTION");
            updatePayload.put("performedBy", assignee);
            updatePayload.put("decision", agentDecision);
            updatePayload.put("comments", agentComments);
            updatePayload.put("status", "IN_PROGRESS");

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(updatePayload, headers);

            // Update complaint status in workplace learning service
            String updateUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/agent-action";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                updateUrl, 
                HttpMethod.PUT, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully updated complaint {} with agent action", complaintId);
                
                // Store the agent's decision for the gateway
                execution.setVariable("agentDecision", agentDecision);
                execution.setVariable("lastAction", "AGENT_HANDLED");
                execution.setVariable("actionTimestamp", System.currentTimeMillis());
                
                // Set additional variables based on decision
                switch (agentDecision.toLowerCase()) {
                    case "close":
                        execution.setVariable("shouldClose", true);
                        execution.setVariable("shouldEscalate", false);
                        execution.setVariable("shouldChat", false);
                        break;
                    case "escalate":
                        execution.setVariable("shouldClose", false);
                        execution.setVariable("shouldEscalate", true);
                        execution.setVariable("shouldChat", false);
                        break;
                    case "chat":
                        execution.setVariable("shouldClose", false);
                        execution.setVariable("shouldEscalate", false);
                        execution.setVariable("shouldChat", true);
                        break;
                    default:
                        log.warn("Unknown agent decision: {}", agentDecision);
                        execution.setVariable("shouldChat", true); // Default to chat
                }
                
            } else {
                log.error("Failed to update complaint {} with agent action", complaintId);
                throw new RuntimeException("Failed to update complaint with agent action");
            }

        } catch (Exception e) {
            log.error("Error in agent handling complaint: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to process agent action: " + e.getMessage());
            throw new RuntimeException("Error in AgentHandleComplaintDelegate", e);
        }
    }
}
