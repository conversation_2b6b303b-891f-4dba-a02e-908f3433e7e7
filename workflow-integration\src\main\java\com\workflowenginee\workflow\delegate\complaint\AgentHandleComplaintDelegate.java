package com.workflowenginee.workflow.delegate.complaint;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

@Component("agentHandleComplaintDelegate")
public class AgentHandleComplaintDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Agent handling complaint: " + complaintId);

        try {
            Map<String, Object> complaintData = (Map<String, Object>) execution.getVariable("complaintData");
            
            if (complaintData != null) {
                // Log the agent action
                System.out.println("[Process: " + processInstanceId + "] Agent is reviewing complaint details");
                System.out.println("Complaint ID: " + complaintId);
                System.out.println("Agent Role: " + role);

                // Set default decision - this will be overridden by the actual agent action
                // The agent decision will be set when the process is resumed via signal
                String defaultDecision = "pending";
                execution.setVariable("agentDecision", defaultDecision);
                execution.setVariable("agentActionTaken", true);

                System.out.println("[Process: " + processInstanceId + "] Agent action recorded, waiting for decision");

            } else {
                System.err.println("[Process: " + processInstanceId + "] No complaint data available for agent handling");
                execution.setVariable("agentActionTaken", false);
                execution.setVariable("agentDecision", "error");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error in agent handling: " + e.getMessage());
            e.printStackTrace();
            execution.setVariable("agentActionTaken", false);
            execution.setVariable("agentDecision", "error");
        }
    }
}
