package bw.org.hrdc.weblogic.workplacelearning.repository.common;

import bw.org.hrdc.weblogic.workplacelearning.entity.common.Quotation;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @CreatedOn 15/04/25 09:19
 * @UpdatedBy martinspectre
 * @UpdatedOn 15/04/25 09:19
 */
public interface QuoteRepository extends JpaRepository<Quotation, UUID>, JpaSpecificationExecutor<Quotation> {
    @Query("SELECT o FROM Quotation o WHERE o.uuid = :uuid")
    Optional<Quotation> findByUuid(String uuid);
    @Query("SELECT o FROM Quotation o WHERE o.reference = :reference")
    Optional<Quotation> findByQuoteReference(String reference);
    @Query("SELECT o FROM Quotation o WHERE o.quoteRef = :quoteRef")
    Optional<Quotation> findByQuoteRef(String quoteRef);

    @Modifying
    @Transactional
    @Query("DELETE FROM Quotation q WHERE q.uuid = :uuid")
    void deleteQuote(@Param("uuid") String uuid);
}