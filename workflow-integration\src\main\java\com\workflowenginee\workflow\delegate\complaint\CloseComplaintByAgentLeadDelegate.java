package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("closeComplaintByAgentLeadDelegate")
public class CloseComplaintByAgentLeadDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String currentAssignee = (String) execution.getVariable("currentAssignee");
            String closureReason = (String) execution.getVariable("closureReason");
            String closureComments = (String) execution.getVariable("closureComments");
            
            log.info("Agent Lead {} closing escalated complaint ID: {}", currentAssignee, complaintId);

            // Prepare closure payload
            Map<String, Object> closurePayload = new HashMap<>();
            closurePayload.put("complaintId", complaintId);
            closurePayload.put("action", "CLOSE_ESCALATED");
            closurePayload.put("performedBy", currentAssignee);
            closurePayload.put("status", "CLOSED");
            closurePayload.put("state", "RESOLVED");
            closurePayload.put("closureReason", closureReason != null ? closureReason : "Resolved by agent lead after escalation");
            closurePayload.put("closureComments", closureComments);
            closurePayload.put("closedBy", "AGENT_LEAD");
            closurePayload.put("wasEscalated", true);

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(closurePayload, headers);

            // Close escalated complaint in workplace learning service
            String closeUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/close-escalated";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                closeUrl, 
                HttpMethod.PUT, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully closed escalated complaint {} by agent lead {}", complaintId, currentAssignee);
                
                execution.setVariable("complaintClosed", true);
                execution.setVariable("closedBy", "AGENT_LEAD");
                execution.setVariable("lastAction", "CLOSED_BY_AGENT_LEAD");
                execution.setVariable("closureTimestamp", System.currentTimeMillis());
                execution.setVariable("escalatedAndClosed", true);
                
                // Send closure notification to complainant
                sendEscalatedClosureNotification(execution, complaintId, currentAssignee);
                
            } else {
                log.error("Failed to close escalated complaint {} by agent lead {}", complaintId, currentAssignee);
                throw new RuntimeException("Failed to close escalated complaint");
            }

        } catch (Exception e) {
            log.error("Error closing escalated complaint: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to close escalated complaint: " + e.getMessage());
            throw new RuntimeException("Error in CloseComplaintByAgentLeadDelegate", e);
        }
    }

    private void sendEscalatedClosureNotification(DelegateExecution execution, String complaintId, String closedBy) {
        try {
            String organisationId = (String) execution.getVariable("organisationId");
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("organisationId", organisationId);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("action", "CLOSED_AFTER_ESCALATION");
            notificationPayload.put("closedBy", closedBy);
            notificationPayload.put("closedByRole", "AGENT_LEAD");
            notificationPayload.put("wasEscalated", true);
            notificationPayload.put("message", "Your escalated complaint has been reviewed and resolved");
            notificationPayload.put("type", "COMPLAINT_ESCALATED_CLOSURE");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            restTemplate.exchange(notificationUrl, HttpMethod.POST, entity, Map.class);
            
            log.info("Escalated closure notification sent for complaint {}", complaintId);
            
        } catch (Exception e) {
            log.warn("Failed to send escalated closure notification for complaint {}: {}", complaintId, e.getMessage());
        }
    }
}
