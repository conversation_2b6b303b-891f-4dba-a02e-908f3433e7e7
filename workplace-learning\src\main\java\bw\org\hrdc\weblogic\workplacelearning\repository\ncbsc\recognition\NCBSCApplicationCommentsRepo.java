package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.LearningOutcome;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplicationComments;
import lombok.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 22/02/25 11:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 22/02/25 11:08
 */
@Repository
public interface NCBSCApplicationCommentsRepo extends JpaRepository<NCBSCApplicationComments, Long>, JpaSpecificationExecutor<LearningOutcome> {
    @Query(value = "SELECT * FROM ncbsc_application_comments a WHERE a.ncbsc_application_id = :applicationId OR a.noc_application_id = :applicationId", nativeQuery = true)
    @NonNull
    List<NCBSCApplicationComments> findByCommentsApplicationId(@NonNull String applicationId);
}
