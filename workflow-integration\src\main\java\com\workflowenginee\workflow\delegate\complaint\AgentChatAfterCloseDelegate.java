package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("agentChatAfterCloseDelegate")
public class AgentChatAfterCloseDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String assignee = (String) execution.getVariable("assignee");
            String followUpMessage = (String) execution.getVariable("followUpMessage");
            String organisationId = (String) execution.getVariable("organisationId");
            
            log.info("Agent {} initiating follow-up chat after closing complaint ID: {}", assignee, complaintId);

            // Prepare follow-up chat payload
            Map<String, Object> chatPayload = new HashMap<>();
            chatPayload.put("complaintId", complaintId);
            chatPayload.put("action", "FOLLOW_UP_CHAT");
            chatPayload.put("performedBy", assignee);
            chatPayload.put("message", followUpMessage != null ? followUpMessage : 
                "Your complaint has been resolved. Please let us know if you need any further assistance.");
            chatPayload.put("chatType", "FOLLOW_UP");
            chatPayload.put("chatInitiatedBy", "AGENT");
            chatPayload.put("isFollowUp", true);

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(chatPayload, headers);

            // Add follow-up chat to complaint
            String chatUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/follow-up-chat";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                chatUrl, 
                HttpMethod.POST, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully initiated follow-up chat for complaint {} by agent {}", complaintId, assignee);
                
                execution.setVariable("followUpChatInitiated", true);
                execution.setVariable("followUpChatBy", "AGENT");
                execution.setVariable("lastAction", "FOLLOW_UP_CHAT_INITIATED");
                execution.setVariable("followUpChatTimestamp", System.currentTimeMillis());
                
                // Send notification to client about follow-up
                sendFollowUpNotification(execution, complaintId, assignee, organisationId);
                
            } else {
                log.error("Failed to initiate follow-up chat for complaint {} by agent {}", complaintId, assignee);
                // Don't throw exception as this is a follow-up action after closure
                execution.setVariable("followUpChatInitiated", false);
            }

        } catch (Exception e) {
            log.error("Error initiating follow-up chat: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to initiate follow-up chat: " + e.getMessage());
            execution.setVariable("followUpChatInitiated", false);
            // Don't throw exception as this is a follow-up action after closure
        }
    }

    private void sendFollowUpNotification(DelegateExecution execution, String complaintId, String agentId, String organisationId) {
        try {
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("organisationId", organisationId);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("agentId", agentId);
            notificationPayload.put("action", "FOLLOW_UP_CHAT");
            notificationPayload.put("message", "Follow-up message regarding your resolved complaint");
            notificationPayload.put("type", "COMPLAINT_FOLLOW_UP");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            restTemplate.exchange(notificationUrl, HttpMethod.POST, entity, Map.class);
            
            log.info("Follow-up notification sent for complaint {}", complaintId);
            
        } catch (Exception e) {
            log.warn("Failed to send follow-up notification for complaint {}: {}", complaintId, e.getMessage());
        }
    }
}
