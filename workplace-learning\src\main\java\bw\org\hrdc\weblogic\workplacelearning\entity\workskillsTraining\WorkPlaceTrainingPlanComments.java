package bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "workplace_training_plan_comments")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkPlaceTrainingPlanComments {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "training_plan_id", nullable = false)
    private WorkPlaceTrainingPlan trainingPlan;

    @Column(name = "action", nullable = false)
    private String action;

    @Column(name = "comments", columnDefinition = "TEXT")
    private String comments;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;

    @Column(name = "batch_id", columnDefinition = "uuid")
    private UUID batchId;
}