package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.config.FeignConfig;
import com.workflowenginee.workflow.util.ApiResponse;

import java.util.UUID;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "WORKPLACE-LEARNING", fallback = WorkplaceLearningClientFallback.class, configuration = FeignConfig.class)
public interface WorkplaceLearningClient {
    @GetMapping("/api/v1/ncbsc/applications/recognition/{referenceNumber}")
    ApiResponse<?> getApplicationByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/pre-approval-applications/application-number/{applicationId}")
    ApiResponse<?> getApplicationById(@PathVariable String applicationId);

    @GetMapping("/api/v1/ncbsc/workplace-training-plan/application-id/{id}")
    ApiResponse<?> getApplicationById(@PathVariable UUID id);

    @GetMapping("/api/v1/ncbsc/noc/reference-number/{referenceNumber}")
    ApiResponse<?> searchByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/complaints/{id}")
    ApiResponse<?>  getComplaintById(@PathVariable String id);
}
