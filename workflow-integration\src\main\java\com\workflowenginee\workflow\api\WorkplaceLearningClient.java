package com.workflowenginee.workflow.api;

import com.workflowenginee.workflow.config.FeignConfig;
import com.workflowenginee.workflow.util.ApiResponse;

import java.util.Map;
import java.util.UUID;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "WORKPLACE-LEARNING", url = "${workplace.learning.service.url:http://localhost:8080}", fallback = WorkplaceLearningClientFallback.class, configuration = FeignConfig.class)
public interface WorkplaceLearningClient {
    @GetMapping("/api/v1/ncbsc/applications/recognition/{referenceNumber}")
    ApiResponse<?> getApplicationByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/pre-approval-applications/application-number/{applicationId}")
    ApiResponse<?> getApplicationById(@PathVariable String applicationId);

    @GetMapping("/api/v1/ncbsc/workplace-training-plan/application-id/{id}")
    ApiResponse<?> getApplicationById(@PathVariable UUID id);

    @GetMapping("/api/v1/ncbsc/noc/reference-number/{referenceNumber}")
    ApiResponse<?> searchByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/complaints/{id}")
    ApiResponse<?> getComplaintById(@PathVariable String id);

    @PutMapping("/api/v1/complaints/{id}/status")
    ApiResponse<?> updateComplaintStatus(@PathVariable String id, @RequestBody Map<String, Object> statusUpdate);

    @PutMapping("/api/v1/complaints/{id}/escalate")
    ApiResponse<?> escalateComplaint(@PathVariable String id, @RequestBody Map<String, Object> escalationData);

    @PostMapping("/api/v1/complaints/{id}/comments")
    ApiResponse<?> addComplaintComment(@PathVariable String id, @RequestBody Map<String, Object> comment);

    @PutMapping("/api/v1/complaints/{id}/assign")
    ApiResponse<?> assignComplaint(@PathVariable String id, @RequestBody Map<String, Object> assignmentData);

    @PutMapping("/api/v1/complaints/{id}/close")
    ApiResponse<?> closeComplaint(@PathVariable String id, @RequestBody Map<String, Object> closureData);
}
