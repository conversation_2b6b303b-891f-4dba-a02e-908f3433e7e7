# Updated API Documentation - Complaint Lifecycle Workflow

## ✅ **Controller and BPMN Flow Alignment**

The controller has been updated to properly align with the BPMN XML flow and delegate logic.

## 🔄 **Complete Workflow Flow**

### **BPMN Process Flow:**
```
Start → Fetch Complaint Details → Notify Agent → 
Intermediate Signal (signalNotifyAgent) → Fetch After Signal → 
Agent Decision Gateway → [Close Path | Escalate Path]
```

### **Signal Names in BPMN:**
- `NotifyAgent` - First intermediate signal
- `Escalated` - Second intermediate signal (for escalation path)

## 📋 **API Endpoints**

### **1. Start Complaint Lifecycle**
```bash
POST /api/v1/workflow/start-complaint-lifecycle/{complaintId}
```

**Example:**
```bash
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-complaint-001
```

**Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle process started successfully",
  "processInstanceId": "3dca7aac-4b4d-11f0-be36-14857f938c25",
  "complaintId": "test-complaint-001"
}
```

### **2. Resume Complaint Lifecycle (Updated)**
```bash
POST /api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/{signalType}
```

#### **Signal Types:**
- `NotifyAgent` - For agent decision (close/escalate)
- `Escalated` - For escalation completion

### **3. Agent Decision - Close Path**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-001",
  "decision": "close"
}
```

**Flow Result:**
```
Signal → FetchAfterSignalDelegate (uses manual decision) → 
Gateway (agentDecision = 'close') → Agent Close → Send Client Notification → End
```

### **4. Agent Decision - Escalate Path**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/NotifyAgent
Content-Type: application/json

{
  "complaintId": "test-complaint-001",
  "decision": "escalate"
}
```

**Flow Result:**
```
Signal → FetchAfterSignalDelegate (uses manual decision) → 
Gateway (agentDecision = 'escalate') → Notify Escalation → 
Intermediate Signal (signalEscalated) → [Wait for Escalated signal]
```

### **5. Complete Escalation**
```bash
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/Escalated
Content-Type: application/json

{
  "complaintId": "test-complaint-001"
}
```

**Flow Result:**
```
Signal → FetchAfterEscalationDelegate → 
Agent Lead Close → Send Client Notification → End
```

### **6. Check Status**
```bash
GET /api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

**Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle status retrieved successfully",
  "processInstanceId": "3dca7aac-4b4d-11f0-be36-14857f938c25",
  "processDefinitionKey": "complaintLifecycleProcess",
  "activeActivities": ["signalNotifyAgent"],
  "variables": {
    "complaintId": "test-complaint-001",
    "complaintData": {...},
    "agentDecision": "close"
  },
  "isEnded": false
}
```

## 🔧 **Updated Controller Logic**

### **Key Changes:**
1. **Simplified Signal Handling**: Only handles `NotifyAgent` and `Escalated` signals
2. **Manual Decision Support**: Passes `manualDecision` to delegates
3. **Proper Error Handling**: Returns appropriate error messages for invalid signals

### **Controller Code:**
```java
if ("NotifyAgent".equals(signalType)) {
    // Pass manual decision to FetchAfterSignalDelegate
    if (decision != null) {
        variables.put("manualDecision", decision);
    }
    runtimeService.signalEventReceived("NotifyAgent", processInstanceId, variables);
    
} else if ("Escalated".equals(signalType)) {
    // Trigger escalation completion
    runtimeService.signalEventReceived("Escalated", processInstanceId, variables);
}
```

## 🎯 **Updated Delegate Logic**

### **FetchAfterSignalDelegate Changes:**
```java
// Check if manual decision was provided from controller
String manualDecision = (String) execution.getVariable("manualDecision");
String agentDecision;

if (manualDecision != null && ("close".equals(manualDecision) || "escalate".equals(manualDecision))) {
    // Use manual decision from controller
    agentDecision = manualDecision;
} else {
    // Determine agent decision based on complaint data
    agentDecision = determineAgentDecision(complaintData);
}

execution.setVariable("agentDecision", agentDecision);
```

## 🧪 **Complete Testing Sequence**

### **Test 1: Agent Close Path**
```bash
# Step 1: Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-001

# Step 2: Agent decides to close
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/NotifyAgent
{
  "complaintId": "test-001",
  "decision": "close"
}

# Step 3: Check final status
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Expected: Process ended, agentDecision = "close", clientNotified = true
```

### **Test 2: Escalation Path**
```bash
# Step 1: Start workflow
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/test-002

# Step 2: Agent decides to escalate
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/NotifyAgent
{
  "complaintId": "test-002",
  "decision": "escalate"
}

# Step 3: Check status (should be waiting at signalEscalated)
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Step 4: Complete escalation
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processId}/Escalated
{
  "complaintId": "test-002"
}

# Step 5: Check final status
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processId}

# Expected: Process ended, closedBy = "AGENT_LEAD", clientNotified = true
```

## 📊 **Process Variables**

### **Key Variables:**
- `complaintId`: Complaint identifier
- `complaintData`: Complete complaint information
- `agentDecision`: "close" or "escalate" (set by FetchAfterSignalDelegate)
- `manualDecision`: Manual decision from controller (optional)
- `escalationDecision`: Always "close" for escalated complaints
- `closedBy`: "AGENT" or "AGENT_LEAD"
- `clientNotified`: Boolean indicating if client was notified

## ✅ **Benefits of Updated Implementation**

1. **✅ Proper Alignment**: Controller logic matches BPMN flow
2. **✅ Flexible Decision Making**: Supports both manual and automatic decisions
3. **✅ Clear Error Messages**: Proper validation and error responses
4. **✅ Simplified API**: Only two signal types needed
5. **✅ Robust Fallbacks**: Handles missing data gracefully

## 🚀 **Ready for Testing**

The updated implementation now properly aligns the controller with the BPMN XML flow and delegate logic. The API calls should work correctly with the provided request body format.

**Status**: ✅ **Controller and BPMN Flow Fully Aligned**
