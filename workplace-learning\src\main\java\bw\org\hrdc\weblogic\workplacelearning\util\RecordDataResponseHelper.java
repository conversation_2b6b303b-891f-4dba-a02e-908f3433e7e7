package bw.org.hrdc.weblogic.workplacelearning.util;

import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @CreatedOn 08/06/25 09:39
 * @UpdatedBy martinspectre
 * @UpdatedOn 08/06/25 09:39
 */
public class RecordDataResponseHelper<T> {

    public Map<String, Object> createMetadata(Page<T> page, Map<String, Long> statusCounts) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", page.getNumber());
        metadata.put("totalPages", page.getTotalPages());
        metadata.put("totalElements", page.getTotalElements());
        metadata.put("pageSize", page.getSize());

        if(!statusCounts.isEmpty()){
            metadata.put("statusCounts", statusCounts);
        }

        return metadata;
    }

    public Map<String, Object> createEmptyResponse(int pageNumber, int size) {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", pageNumber);
        metadata.put("totalPages", 0);
        metadata.put("totalElements", 0L);
        metadata.put("pageSize", size);

        // Add empty status counts with all the new fields
        Map<String, Object> statusCounts = new HashMap<>();
        statusCounts.put("totalApplications", 0L);
        statusCounts.put("pendingVetting", 0L);
        statusCounts.put("vettingOngoing", 0L);
        statusCounts.put("awaitingChanges", 0L);
        statusCounts.put("rejected", 0L);
        statusCounts.put("approved", 0L);
        statusCounts.put("underReview", 0L);
        statusCounts.put("pendingApproval", 0L);

        metadata.put("statusCounts", statusCounts);
        response.put("metadata", metadata);
        response.put("content", new ArrayList<>());
        return response;
    }

}
