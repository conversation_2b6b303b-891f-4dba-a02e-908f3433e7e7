package bw.org.hrdc.weblogic.workplacelearning.service;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;

import java.util.List;
import java.util.UUID;

public interface WorkPlaceTrainingPlanCommentsService {

    /**
     * Create a new comment for a training plan
     * @param comment The comment to create
     * @return The created comment
     */
    WorkPlaceTrainingPlanComments createComments(WorkPlaceTrainingPlanComments comment);

    /**
     * Get all audit logs for a training plan
     * @param trainingPlanId The ID of the training plan
     * @return List of comments for the training plan
     */
    List<WorkPlaceTrainingPlanComments> getAuditLogsForTrainingPlan(UUID trainingPlanId);
}