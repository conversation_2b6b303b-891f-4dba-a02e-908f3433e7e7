package bw.org.hrdc.weblogic.workplacelearning.mapper;

import bw.org.hrdc.weblogic.workplacelearning.dto.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.*;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Mapper for converting between PreApprovalApplication entity and PreApprovalApplicationDto.
 */
@Component
public class PreApprovalApplicationMapper {

    private static final Logger logger = LoggerFactory.getLogger(PreApprovalApplicationMapper.class);
    private final ModelMapper modelMapper;

    @Autowired
    public PreApprovalApplicationMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
        configureModelMapper();
    }

    public ModelMapper getModelMapper() {
        return modelMapper;
    }

    /**
     * Configure ModelMapper with custom mappings and validation.
     */
    private void configureModelMapper() {
        // Add custom mapping for UUID
        Converter<String, UUID> stringToUUIDConverter = context -> {
            try {
                return context.getSource() == null ? null : UUID.fromString(context.getSource());
            } catch (IllegalArgumentException e) {
                logger.error("Invalid UUID format: {}", context.getSource());
                return null;
            }
        };

        Converter<UUID, String> uuidToStringConverter = context ->
                context.getSource() == null ? null : context.getSource().toString();

        modelMapper.addConverter(stringToUUIDConverter, String.class, UUID.class);
        modelMapper.addConverter(uuidToStringConverter, UUID.class, String.class);

        // Skip nested list mapping for manual handling
        modelMapper.typeMap(PreApprovalApplicationDto.class, PreApprovalApplication.class)
                .addMappings(mapper -> {
                    mapper.skip(PreApprovalApplication::setParticularOfTrainings);
                    mapper.skip(PreApprovalApplication::setModuleDetails);
                    mapper.skip(PreApprovalApplication::setEstimatedTrainingCosts);
                    mapper.skip(PreApprovalApplication::setCreatedDate);
                    mapper.skip(PreApprovalApplication::setVatNumber);
                });
    }

    /**
     * Convert Entity to DTO with proper validation.
     *
     * @param entity the PreApprovalApplication entity to convert
     * @return the corresponding PreApprovalApplicationDto
     */
    public PreApprovalApplicationDto toDto(PreApprovalApplication entity) {
        if (entity == null) {
            logger.warn("Attempting to convert null entity to DTO");
            return null;
        }

        try {
            PreApprovalApplicationDto dto = modelMapper.map(entity, PreApprovalApplicationDto.class);

            // Map child lists with null checks
            if (entity.getParticularOfTrainings() != null) {
                dto.setParticularOfTrainings(
                        entity.getParticularOfTrainings()
                                .stream()
                                .map(e -> modelMapper.map(e, ParticularOfTrainingDto.class))
                                .collect(Collectors.toList())
                );
            }

            if (entity.getModuleDetails() != null) {
                dto.setModuleDetails(
                        entity.getModuleDetails()
                                .stream()
                                .map(e -> modelMapper.map(e, ModuleDetailsDto.class))
                                .collect(Collectors.toList())
                );
            }

            if (entity.getEstimatedTrainingCosts() != null) {
                dto.setEstimatedTrainingCosts(
                        entity.getEstimatedTrainingCosts()
                                .stream()
                                .map(e -> modelMapper.map(e, EstimatedTrainingCostsDto.class))
                                .collect(Collectors.toList())
                );
            }

            return dto;
        } catch (Exception e) {
            logger.error("Error converting entity to DTO: {}", e.getMessage());
            throw new RuntimeException("Error converting entity to DTO", e);
        }
    }

    /**
     * Convert DTO to Entity with proper validation.
     *
     * @param dto the PreApprovalApplicationDto to convert
     * @return the corresponding PreApprovalApplication entity
     */
    public PreApprovalApplication toEntity(PreApprovalApplicationDto dto) {
        if (dto == null) {
            logger.warn("Attempting to convert null DTO to entity");
            return null;
        }

        try {
            PreApprovalApplication entity = modelMapper.map(dto, PreApprovalApplication.class);

            // Map child lists with null checks
            if (dto.getParticularOfTrainings() != null) {
                entity.setParticularOfTrainings(
                        dto.getParticularOfTrainings()
                                .stream()
                                .map(d -> {
                                    ParticularOfTraining training = modelMapper.map(d, ParticularOfTraining.class);
                                    training.setPreApprovalApplication(entity);
                                    return training;
                                })
                                .collect(Collectors.toList())
                );
            }

            if (dto.getModuleDetails() != null) {
                entity.setModuleDetails(
                        dto.getModuleDetails()
                                .stream()
                                .map(d -> {
                                    ModuleDetails module = modelMapper.map(d, ModuleDetails.class);
                                    module.setPreApprovalApplication(entity);
                                    if (d.getModuleReferenceId() != null) {
                                        module.setModuleReferenceId(d.getModuleReferenceId());
                                    }
                                    return module;
                                })
                                .collect(Collectors.toList())
                );
            }

            if (dto.getEstimatedTrainingCosts() != null) {
                entity.setEstimatedTrainingCosts(
                        dto.getEstimatedTrainingCosts()
                                .stream()
                                .map(d -> {
                                    EstimatedTrainingCosts cost = modelMapper.map(d, EstimatedTrainingCosts.class);
                                    cost.setPreApprovalApplication(entity);
                                    return cost;
                                })
                                .collect(Collectors.toList())
                );
            }

            return entity;
        } catch (Exception e) {
            logger.error("Error converting DTO to entity: {}", e.getMessage());
            throw new RuntimeException("Error converting DTO to entity", e);
        }
    }

    /**
     * Update an existing entity with data from a DTO, preserving critical fields.
     *
     * @param dto the PreApprovalApplicationDto containing updated data
     * @param entity the existing PreApprovalApplication entity to update
     */
    public void updateEntityFromDto(PreApprovalApplicationDto dto, PreApprovalApplication entity) {
        if (dto == null || entity == null) {
            logger.warn("Attempting to update with null DTO or entity");
            return;
        }

        try {
            // Store current values that should be preserved
            String currentStatus = entity.getStatus();
            String currentState = entity.getState();
            Date currentCreatedDate = entity.getCreatedDate();
            String currentVatNumber = entity.getVatNumber();
            String currentApplicationNumber = entity.getApplicationNumber();
            String currentReferenceNumber = entity.getReferenceNumber();
            String currentAssignedAgent = entity.getAssignedAgent();
            String currentAssignedAgentLead = entity.getAssignedAgentLead();
            String currentAssignedOfficer = entity.getAssignedOfficer();
            String currentAssignedOfficerLead = entity.getAssignedOfficerLead();
            String currentAssignedManager = entity.getAssignedManager();
            String currentCreatedBy = entity.getCreatedBy();
            Date currentLastModifiedDate = entity.getLastModifiedDate();
            Boolean currentDeleted = entity.getDeleted();
            Date currentDeletedDate = entity.getDeletedDate();

            // Map basic properties
            modelMapper.map(dto, entity);

            // Restore values if they were null in the DTO
            if (dto.getStatus() == null && currentStatus != null) {
                entity.setStatus(currentStatus);
            }

            if (dto.getState() == null && currentState != null) {
                entity.setState(currentState);
            }

            // Always preserve these critical fields
            entity.setCreatedDate(currentCreatedDate);
            
            // Preserve other important fields if they're null in the DTO
            if (dto.getVatNumber() == null && currentVatNumber != null) {
                entity.setVatNumber(currentVatNumber);
            }
            
            if (dto.getApplicationNumber() == null && currentApplicationNumber != null) {
                entity.setApplicationNumber(currentApplicationNumber);
            }
            
            if (dto.getReferenceNumber() == null && currentReferenceNumber != null) {
                entity.setReferenceNumber(currentReferenceNumber);
            }
            
            // Preserve workflow assignment fields
            if (dto.getAssignedAgent() == null && currentAssignedAgent != null) {
                entity.setAssignedAgent(currentAssignedAgent);
            }
            
            if (dto.getAssignedAgentLead() == null && currentAssignedAgentLead != null) {
                entity.setAssignedAgentLead(currentAssignedAgentLead);
            }
            
            if (dto.getAssignedOfficer() == null && currentAssignedOfficer != null) {
                entity.setAssignedOfficer(currentAssignedOfficer);
            }
            
            if (dto.getAssignedOfficerLead() == null && currentAssignedOfficerLead != null) {
                entity.setAssignedOfficerLead(currentAssignedOfficerLead);
            }
            
            if (dto.getAssignedManager() == null && currentAssignedManager != null) {
                entity.setAssignedManager(currentAssignedManager);
            }
            
            // Preserve audit fields
            if (dto.getCreatedBy() == null && currentCreatedBy != null) {
                entity.setCreatedBy(currentCreatedBy);
            }
            
            // Preserve soft delete fields
            entity.setDeleted(currentDeleted);
            if (currentDeletedDate != null) {
                entity.setDeletedDate(currentDeletedDate);
            }

            // Handle collections separately to avoid replacing existing collections
            if (dto.getParticularOfTrainings() != null) {
                entity.getParticularOfTrainings().clear();
                entity.getParticularOfTrainings().addAll(
                        dto.getParticularOfTrainings()
                                .stream()
                                .map(d -> {
                                    ParticularOfTraining training = modelMapper.map(d, ParticularOfTraining.class);
                                    training.setPreApprovalApplication(entity);
                                    return training;
                                })
                                .collect(Collectors.toList())
                );
            }

            if (dto.getModuleDetails() != null) {
                entity.getModuleDetails().clear();
                entity.getModuleDetails().addAll(
                        dto.getModuleDetails()
                                .stream()
                                .map(d -> {
                                    ModuleDetails module = modelMapper.map(d, ModuleDetails.class);
                                    module.setPreApprovalApplication(entity);
                                    if (d.getModuleReferenceId() != null) {
                                        module.setModuleReferenceId(d.getModuleReferenceId());
                                    }
                                    return module;
                                })
                                .collect(Collectors.toList())
                );
            }

            if (dto.getEstimatedTrainingCosts() != null) {
                entity.getEstimatedTrainingCosts().clear();
                entity.getEstimatedTrainingCosts().addAll(
                        dto.getEstimatedTrainingCosts()
                                .stream()
                                .map(d -> {
                                    EstimatedTrainingCosts cost = modelMapper.map(d, EstimatedTrainingCosts.class);
                                    cost.setPreApprovalApplication(entity);
                                    return cost;
                                })
                                .collect(Collectors.toList())
                );
            }
        } catch (Exception e) {
            logger.error("Error updating entity from DTO: {}", e.getMessage());
            throw new RuntimeException("Error updating entity from DTO", e);
        }
    }

    /**
     * Convert a Page of PreApprovalApplication entities to a Page of PreApprovalApplicationDto.
     *
     * @param entityPage the Page of PreApprovalApplication entities
     * @return a Page of PreApprovalApplicationDto
     */
    public Page<PreApprovalApplicationDto> toDtoPage(Page<PreApprovalApplication> entityPage) {
        return entityPage.map(this::toDto);
    }

    /**
     * Convert a List of PreApprovalApplication entities to a List of PreApprovalApplicationDto.
     *
     * @param entityList the List of PreApprovalApplication entities
     * @return a List of PreApprovalApplicationDto
     */
    public List<PreApprovalApplicationDto> toDtoList(List<PreApprovalApplication> entityList) {
        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert a List of PreApprovalApplicationDto to a List of PreApprovalApplication entities.
     *
     * @param dtoList the List of PreApprovalApplicationDto
     * @return a List of PreApprovalApplication entities
     */
    public List<PreApprovalApplication> toEntityList(List<PreApprovalApplicationDto> dtoList) {
        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
