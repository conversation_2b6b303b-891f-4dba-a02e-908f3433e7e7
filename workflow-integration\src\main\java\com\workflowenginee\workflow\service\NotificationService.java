package com.workflowenginee.workflow.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;

import com.netflix.spectator.impl.PatternExpr.Not;
import com.workflowenginee.workflow.api.CompanyClient;
import com.workflowenginee.workflow.dto.NotificationDTO;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@Service
public class NotificationService {

     private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    @Qualifier("notificationKafkaTemplate")
    private KafkaTemplate<String, NotificationDTO> kafkaTemplate;

    @Value("${spring.kafka.template.default-topic}")
    private String notificationTopic;

    @Autowired
    private CompanyClient companyClient;

    public void notifyUsersByRole(NotifyUsersByRoleDto dto) {
        String role = dto.getRole();
        String referenceNumber = dto.getReferenceNumber();
        String applicationId = dto.getApplicationId();
        String applicationStatus = dto.getApplicationStatus();
        String applicationType = dto.getApplicationType();

        logger.info("Sending notifications to users with role: {}", role);
        System.out.println("role ::  "+role);

        String subject = "Application Notification";
        String message = "Notification for application " + referenceNumber;
        String type = Enums.NotificationType.IN_APP.name();
        String status = Enums.Status.PENDING.name();

        // Customize subject and message based on role
        if (Enums.Role.AGENT_LEAD.name().equalsIgnoreCase(role)) {
            subject = "Application Submitted";
            message = "A new application " + referenceNumber + " has been created and is ready for assignment";
        } else if (Enums.Role.OFFICER_LEAD.name().equalsIgnoreCase(role)) {
            subject = "Application Approved by Agent";
            message = "Application " + referenceNumber + " has been approved by agent and requires assignment";
        } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role)) {
            subject = "Application Approved by Officer";
            message = "Application " + referenceNumber + " has been approved by officer and requires review";
        }

        try {
            String assignRole = role.toLowerCase();
            // Call external service to fetch users
            ApiResponse<?> response = companyClient.fetchAllActiveUsers(assignRole);

            if (response == null || !response.isStatus()) {
                logger.warn("Failed to retrieve users with role: {} - Empty or unsuccessful response", role);
                return;
            }

            Object data = response.getData();
            if (!(data instanceof List)) {
                logger.error("Unexpected data type in response for role {}: Expected List, got {}", role, data.getClass().getSimpleName());
                return;
            }

            // @SuppressWarnings("unchecked")
            List<Map<String, Object>> users = (List<Map<String, Object>>) data;

            if (users.isEmpty()) {
                logger.info("No active users found with role: {}", role);
                return;
            }

            for (Map<String, Object> user : users) {
                try {
                    String userId = (String) user.get("id");
                    String username = (String) user.get("username");

                    if (userId == null || username == null) {
                        logger.warn("Skipping user due to missing ID or username: {}", user);
                        continue;
                    }

                    if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType) || Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {
                        sendApplicationNotification(userId, username, subject, message, type, status, applicationType, applicationId);

                    }else{
                        sendApplicationNotification(userId, username, subject, message, type, status, applicationType, referenceNumber);
                    }

                } catch (Exception userNotificationEx) {
                    logger.error("Error sending notification to user {}: {}", user.get("username"), userNotificationEx.getMessage(), userNotificationEx);
                    // Continue with next user
                }
            }

            logger.info("Successfully sent notifications to {} users with role {}", users.size(), role);

        } catch (Exception e) {
            logger.error("Error retrieving or processing users with role {}: {}", role, e.getMessage(), e);
        }
    }

    public void notifyToClient(NotifyToClientDto dto) {
            String referenceNumber = dto.getReferenceNumber();
            String applicationId = dto.getApplicationId();
            String applicationStatus = dto.getApplicationStatus();
            String companyId = dto.getCompanyId();
            String applicationState = dto.getApplicationState();
            String applicationType = dto.getApplicationType();

        logger.info("Sending notifications to users with companyId: {}, state: {}, status: {}", companyId, applicationState, applicationStatus);

        String subject = "Application Status Update";
        String message = "Your application " + referenceNumber + " has been updated";
        String status = Enums.Status.PENDING.name();

        // Customize message based on application state and status
        if (applicationState != null && applicationStatus != null) {
            if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Approved by Agent";
                message = "Your application " + referenceNumber + " has been approved by the agent and is under review";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Approved by Officer";
                message = "Your application " + referenceNumber + " has been approved by the officer and is awaiting final approval";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Approved";
                message = "Your application " + referenceNumber + " has been approved by the manager";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Rejected";
                message = "We regret to inform you that your application " + referenceNumber + " has been rejected";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Additional Information Required";
                message = "Your application " + referenceNumber + " requires additional information. Please review and update accordingly";
            }
        }

        try {
            ApiResponse<?> response = companyClient.fetchCompanyById(companyId);

            if (response == null || !response.isStatus() || response.getData() == null) {
                logger.warn("Failed to retrieve company data for companyId: {}", companyId);
                return;
            }

            Object responseData = response.getData();
            if (!(responseData instanceof Map)) {
                logger.error("Unexpected response format for company data. Expected Map, found: {}", responseData.getClass().getSimpleName());
                return;
            }

            Map<String, Object> companyData = (Map<String, Object>) responseData;
            Map<String, Object> contactPerson;

            try {
                contactPerson = (Map<String, Object>) companyData.get("contactPerson");
            } catch (ClassCastException e) {
                logger.error("Error casting contactPerson data for companyId {}: {}", companyId, e.getMessage(), e);
                return;
            }

            if (contactPerson == null) {
                logger.warn("No contact person found for companyId: {}", companyId);
                return;
            }

            String clientName = (String) contactPerson.get("name");
            String clientEmail = (String) contactPerson.get("email");

            if (clientEmail == null || clientEmail.isBlank()) {
                logger.warn("Missing client email for companyId: {}", companyId);
                return;
            }

            try {
                logger.debug("Sending notification to contact person: {}", clientEmail);

                if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType) || Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {
                    sendApplicationNotification(companyId, clientEmail, subject, message,Enums.NotificationType.IN_APP.name(), status, applicationType, applicationId);
                    sendApplicationNotification(clientEmail, clientName, subject, message, Enums.NotificationType.EMAIL.name(), status, applicationType, applicationId);

                } else {
                // IN_APP notification
                    sendApplicationNotification(companyId, clientEmail, subject, message,Enums.NotificationType.IN_APP.name(), status, applicationType, referenceNumber);

                // EMAIL notification
                    sendApplicationNotification(clientEmail, clientName, subject, message, Enums.NotificationType.EMAIL.name(), status, applicationType, referenceNumber);
                }
             

                logger.info("Notifications successfully sent to client: {}", clientEmail);
            } catch (Exception notifyEx) {
                logger.error("Error sending notification to client {}: {}", clientEmail, notifyEx.getMessage(), notifyEx);
            }

        } catch (Exception e) {
            logger.error("Error fetching company info or processing client notification for companyId {}: {}", companyId, e.getMessage(), e);
        }
    }

    public void notifySpecificUser(String userId, String referenceNumber, String applicationId, 
                                String subject, String message, String applicationType) {
        logger.info("Sending notification to specific user with ID: {}", userId);

        try {
            // Fetch user details from the company service
            ApiResponse<?> response = companyClient.fetchUserById(userId);

            if (response == null || !response.isStatus() || response.getData() == null) {
                logger.warn("Failed to retrieve user details for ID: {}. Response: {}", userId, response);
                return;
            }

            Object data = response.getData();
            if (!(data instanceof Map)) {
                logger.error("Unexpected user data format for userId {}: Expected Map but got {}", userId, data.getClass().getSimpleName());
                return;
            }

            Map<String, Object> userData = (Map<String, Object>) data;

            String username = (String) userData.get("username");
            String email = (String) userData.get("email");

            if (username == null || username.isBlank()) {
                logger.warn("Missing username for userId: {}", userId);
            }

            // Send IN_APP notification
            try {
                sendApplicationNotification(userId, username, subject, message, Enums.NotificationType.IN_APP.name(), Enums.Status.PENDING.name(), applicationType, referenceNumber);
                logger.debug("IN_APP notification sent to userId: {}", userId);
            } catch (Exception inAppEx) {
                logger.error("Failed to send IN_APP notification to userId {}: {}", userId, inAppEx.getMessage(), inAppEx);
            }

            // Send EMAIL notification if email is present
            if (email != null && !email.isBlank()) {
                try {
                    sendApplicationNotification(email, username, subject, message, Enums.NotificationType.EMAIL.name(), Enums.Status.PENDING.name(), applicationType, referenceNumber);
                    logger.debug("EMAIL notification sent to email: {}", email);
                } catch (Exception emailEx) {
                    logger.error("Failed to send EMAIL notification to email {}: {}", email, emailEx.getMessage(), emailEx);
                }
            } else {
                logger.warn("No valid email found for userId: {}", userId);
            }

            logger.info("Notification attempt completed for userId: {}", userId);

        } catch (Exception e) {
            logger.error("Error fetching or notifying user with ID {}: {}", userId, e.getMessage(), e);
        }
    }


    private void sendApplicationNotification(String userId, String username, String subject, String message,
                                            String type, String status, String applicationType, String applicationId) {
        if (userId == null || subject == null || message == null || type == null || status == null) {
            logger.warn("Missing required notification parameters: userId={}, subject={}, type={}, status={}", 
                        userId, subject, type, status);
            return;
        }

        try {
            NotificationDTO notification = NotificationDTO.builder()
                .id(UUID.randomUUID().toString())
                .name(username)
                .subject(subject)
                .recipient(userId)
                .message(message)
                .sentAt(LocalDateTime.now().toString())
                .type(type)
                .status(status)
                .applicationType(applicationType)
                .applicationId(applicationId)
                .build();

            kafkaTemplate.send(notificationTopic, notification)
                .whenComplete((result, ex) -> {
                    if (ex == null) {
                        try {
                            logger.info("Notification sent to user [{}] - topic: {}, offset: {}, partition: {}",
                                    username,
                                    result.getRecordMetadata().topic(),
                                    result.getRecordMetadata().offset(),
                                    result.getRecordMetadata().partition());
                        } catch (Exception metaEx) {
                            logger.error("Error reading Kafka metadata for user {}: {}", userId, metaEx.getMessage(), metaEx);
                        }
                    } else {
                        logger.error("Failed to send notification to user [{}]: {}", username, ex.getMessage(), ex);
                    }
                });

        } catch (Exception e) {
            logger.error("Exception while building or sending notification for user [{}]: {}", username, e.getMessage(), e);
        }
    }

}
