package co.bw.hrdc.weblogic.emailsender.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest {
        private String userId;
        private String otpType;
        private String otp;
        private String to;
        private String subject;
        private String name;
        private String body;
        private String contactAddress;
        private String communicationType;
        private String password;
}