package co.bw.hrdc.weblogic.emailsender.dto;

public class EmailRequest {
    private String userId;
    private String otpType;
    private String otp;
    private String to;
    private String subject;
    private String name;
    private String body;
    private String contactAddress;
    private String communicationType;
    private String password;

    // Default constructor
    public EmailRequest() {}

    // Constructor with commonly used fields
    public EmailRequest(String contactAddress, String name, String subject, String body) {
        this.contactAddress = contactAddress;
        this.name = name;
        this.subject = subject;
        this.body = body;
    }

    // Builder pattern implementation
    public static EmailRequestBuilder builder() {
        return new EmailRequestBuilder();
    }

    public static class EmailRequestBuilder {
        private String userId;
        private String otpType;
        private String otp;
        private String to;
        private String subject;
        private String name;
        private String body;
        private String contactAddress;
        private String communicationType;
        private String password;

        public EmailRequestBuilder userId(String userId) {
            this.userId = userId;
            return this;
        }

        public EmailRequestBuilder otpType(String otpType) {
            this.otpType = otpType;
            return this;
        }

        public EmailRequestBuilder otp(String otp) {
            this.otp = otp;
            return this;
        }

        public EmailRequestBuilder to(String to) {
            this.to = to;
            return this;
        }

        public EmailRequestBuilder subject(String subject) {
            this.subject = subject;
            return this;
        }

        public EmailRequestBuilder name(String name) {
            this.name = name;
            return this;
        }

        public EmailRequestBuilder body(String body) {
            this.body = body;
            return this;
        }

        public EmailRequestBuilder contactAddress(String contactAddress) {
            this.contactAddress = contactAddress;
            return this;
        }

        public EmailRequestBuilder communicationType(String communicationType) {
            this.communicationType = communicationType;
            return this;
        }

        public EmailRequestBuilder password(String password) {
            this.password = password;
            return this;
        }

        public EmailRequest build() {
            EmailRequest emailRequest = new EmailRequest();
            emailRequest.userId = this.userId;
            emailRequest.otpType = this.otpType;
            emailRequest.otp = this.otp;
            emailRequest.to = this.to;
            emailRequest.subject = this.subject;
            emailRequest.name = this.name;
            emailRequest.body = this.body;
            emailRequest.contactAddress = this.contactAddress;
            emailRequest.communicationType = this.communicationType;
            emailRequest.password = this.password;
            return emailRequest;
        }
    }

    // Getters and Setters
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }

    public String getOtpType() { return otpType; }
    public void setOtpType(String otpType) { this.otpType = otpType; }

    public String getOtp() { return otp; }
    public void setOtp(String otp) { this.otp = otp; }

    public String getTo() { return to; }
    public void setTo(String to) { this.to = to; }

    public String getSubject() { return subject; }
    public void setSubject(String subject) { this.subject = subject; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getBody() { return body; }
    public void setBody(String body) { this.body = body; }

    public String getContactAddress() { return contactAddress; }
    public void setContactAddress(String contactAddress) { this.contactAddress = contactAddress; }

    public String getCommunicationType() { return communicationType; }
    public void setCommunicationType(String communicationType) { this.communicationType = communicationType; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}