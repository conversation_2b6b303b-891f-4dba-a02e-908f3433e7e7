package com.workflowenginee.workflow.delegate.complaint;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("fetchAfterEscalationDelegate")
public class FetchAfterEscalationDelegate implements JavaDelegate {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            String complaintId = (String) execution.getVariable("complaintId");
            String escalationDecision = (String) execution.getVariable("escalationDecision");
            String escalatedBy = (String) execution.getVariable("escalatedBy");
            String escalationReason = (String) execution.getVariable("escalationReason");
            
            log.info("Processing escalation for complaint ID: {} with decision: {}", complaintId, escalationDecision);

            // Prepare escalation payload
            Map<String, Object> escalationPayload = new HashMap<>();
            escalationPayload.put("complaintId", complaintId);
            escalationPayload.put("action", "ESCALATE");
            escalationPayload.put("performedBy", escalatedBy);
            escalationPayload.put("escalationDecision", escalationDecision);
            escalationPayload.put("escalationReason", escalationReason);
            escalationPayload.put("status", "ESCALATED");
            escalationPayload.put("state", "IN_REVIEW");

            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(escalationPayload, headers);

            // Update complaint with escalation information
            String escalationUrl = "http://localhost:8091/api/v1/complaints/" + complaintId + "/escalate";
            
            ResponseEntity<Map> response = restTemplate.exchange(
                escalationUrl, 
                HttpMethod.PUT, 
                entity, 
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> updatedComplaintData = response.getBody();
                
                // Update process variables with escalated complaint data
                execution.setVariable("escalationDecision", escalationDecision);
                execution.setVariable("escalatedBy", escalatedBy);
                execution.setVariable("escalationTimestamp", System.currentTimeMillis());
                execution.setVariable("lastAction", "ESCALATED");
                execution.setVariable("currentAssignee", updatedComplaintData.get("assignee"));
                execution.setVariable("escalationLevel", updatedComplaintData.get("escalationLevel"));
                
                // Set decision variables for the gateway
                switch (escalationDecision.toLowerCase()) {
                    case "close":
                        execution.setVariable("shouldCloseAfterEscalation", true);
                        execution.setVariable("shouldChatAfterEscalation", false);
                        break;
                    case "chat":
                        execution.setVariable("shouldCloseAfterEscalation", false);
                        execution.setVariable("shouldChatAfterEscalation", true);
                        break;
                    default:
                        log.warn("Unknown escalation decision: {}", escalationDecision);
                        execution.setVariable("shouldChatAfterEscalation", true); // Default to chat
                }
                
                // Send escalation notification
                sendEscalationNotification(execution, complaintId, escalatedBy, escalationDecision);
                
                log.info("Successfully processed escalation for complaint {}", complaintId);
                
            } else {
                log.error("Failed to process escalation for complaint {}", complaintId);
                throw new RuntimeException("Failed to process escalation");
            }

        } catch (Exception e) {
            log.error("Error processing escalation: {}", e.getMessage(), e);
            execution.setVariable("error", "Failed to process escalation: " + e.getMessage());
            throw new RuntimeException("Error in FetchAfterEscalationDelegate", e);
        }
    }

    private void sendEscalationNotification(DelegateExecution execution, String complaintId, String escalatedBy, String decision) {
        try {
            String organisationId = (String) execution.getVariable("organisationId");
            String referenceNumber = (String) execution.getVariable("referenceNumber");
            String currentAssignee = (String) execution.getVariable("currentAssignee");
            
            Map<String, Object> notificationPayload = new HashMap<>();
            notificationPayload.put("complaintId", complaintId);
            notificationPayload.put("organisationId", organisationId);
            notificationPayload.put("referenceNumber", referenceNumber);
            notificationPayload.put("escalatedBy", escalatedBy);
            notificationPayload.put("newAssignee", currentAssignee);
            notificationPayload.put("decision", decision);
            notificationPayload.put("action", "ESCALATED");
            notificationPayload.put("message", "Complaint has been escalated for further review");
            notificationPayload.put("type", "COMPLAINT_ESCALATION");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(notificationPayload, headers);

            String notificationUrl = "http://localhost:8092/api/v1/notifications/send";
            restTemplate.exchange(notificationUrl, HttpMethod.POST, entity, Map.class);
            
            log.info("Escalation notification sent for complaint {}", complaintId);
            
        } catch (Exception e) {
            log.warn("Failed to send escalation notification for complaint {}: {}", complaintId, e.getMessage());
        }
    }
}
