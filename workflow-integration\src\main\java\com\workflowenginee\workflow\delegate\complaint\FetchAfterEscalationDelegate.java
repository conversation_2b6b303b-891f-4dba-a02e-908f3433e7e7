package com.workflowenginee.workflow.delegate.complaint;

import java.util.HashMap;
import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.util.ApiResponse;

@Component("fetchAfterEscalationDelegate")
public class FetchAfterEscalationDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;

    public FetchAfterEscalationDelegate(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String complaintId = (String) execution.getVariable("complaintId");
        String role = (String) execution.getVariable("role");

        System.out.println("[Process: " + processInstanceId + "] Fetching complaint data after escalation: " + complaintId);

        try {
            // Fetch updated complaint data
            ApiResponse<?> response = workplaceLearningClient.getComplaintById(complaintId);

            if (response != null && response.getData() instanceof Map) {
                Map<String, Object> complaintData = (Map<String, Object>) response.getData();
                execution.setVariable("escalatedComplaintData", complaintData);
                execution.setVariable("escalationLevel", "AGENT_LEAD");
                execution.setVariable("escalatedAt", System.currentTimeMillis());

                System.out.println("[Process: " + processInstanceId + "] Successfully fetched escalated complaint data");

                // TODO: Notify agent lead about escalation
                System.out.println("[Process: " + processInstanceId + "] Agent lead would be notified about escalation");

                // Determine escalation decision based on complaint data
                String escalationDecision = determineEscalationDecision(complaintData);
                execution.setVariable("escalationDecision", escalationDecision);

            } else {
                System.err.println("[Process: " + processInstanceId + "] Failed to fetch escalated complaint data");
                
                // Set fallback data
                Map<String, Object> fallbackData = new HashMap<>();
                fallbackData.put("complaintId", complaintId);
                fallbackData.put("status", "ESCALATED");
                fallbackData.put("error", "Failed to fetch escalated complaint details");
                
                execution.setVariable("escalatedComplaintData", fallbackData);
                execution.setVariable("escalationDecision", "error");
            }

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error fetching escalated complaint data: " + e.getMessage());
            e.printStackTrace();

            // Set error data
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("complaintId", complaintId);
            errorData.put("status", "ERROR");
            errorData.put("error", e.getMessage());
            
            execution.setVariable("escalatedComplaintData", errorData);
            execution.setVariable("escalationDecision", "error");
        }
    }

    /**
     * Determines the escalation decision based on complaint data
     */
    private String determineEscalationDecision(Map<String, Object> complaintData) {
        String typeOfComplaint = (String) complaintData.get("typeOfComplaint");
        String priority = (String) complaintData.get("priority");
        String department = (String) complaintData.get("department");
        String status = (String) complaintData.get("status");

        // Critical issues should be closed immediately by agent lead
        if ("CRITICAL".equals(priority) || "URGENT".equals(priority)) {
            return "close";
        }

        // Policy violations need agent lead closure
        if ("POLICY_VIOLATION".equals(typeOfComplaint) || "COMPLIANCE_ISSUE".equals(typeOfComplaint)) {
            return "close";
        }

        // Management or legal department issues need direct closure
        if ("MANAGEMENT".equals(department) || "LEGAL".equals(department)) {
            return "close";
        }

        // Complex complaints that need discussion
        if ("COMPLEX_COMPLAINT".equals(typeOfComplaint) ||
            "SERVICE_QUALITY".equals(typeOfComplaint) ||
            "BILLING_DISPUTE".equals(typeOfComplaint)) {
            return "chat";
        }

        // Customer relationship issues need chat
        if ("CUSTOMER_RELATIONSHIP".equals(typeOfComplaint) ||
            "COMMUNICATION_ISSUE".equals(typeOfComplaint)) {
            return "chat";
        }

        // Default to close for escalated cases (agent lead has authority)
        return "close";
    }
}
