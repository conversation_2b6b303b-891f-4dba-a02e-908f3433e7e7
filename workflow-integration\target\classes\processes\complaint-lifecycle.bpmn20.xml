<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:flowable="http://flowable.org/bpmn"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef">

  <signal id="notifyAgentSignal" name="NotifyAgent"/>
  <signal id="escalatedSignal" name="Escalated"/>

  <process id="complaintLifecycleProcess" name="Complaint Lifecycle Flow" isExecutable="true">

    <!-- Start -->
    <startEvent id="startEvent" name="Complaint Raised"/>

    <!-- Fetch Complaint Details -->
    <serviceTask id="fetchComplaintDetails" name="Fetch Complaint Details" flowable:delegateExpression="${fetchComplaintDelegate}"/>

    <!-- Notify Agent -->
    <serviceTask id="notifyAgent" name="Notify Agent" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Agent Response -->
    <intermediateCatchEvent id="signalNotifyAgent" name="Wait for Agent Response">
      <signalEventDefinition signalRef="notifyAgentSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch After Signal -->
    <serviceTask id="fetchAfterSignal" name="Fetch After Signal" flowable:delegateExpression="${fetchAfterSignalDelegate}"/>

    <!-- Agent Decision Gateway -->
    <exclusiveGateway id="agentDecision" name="Agent Decision"/>
 
    <!-- Close Path -->
    <sequenceFlow id="flow_close_condition" sourceRef="agentDecision" targetRef="agentClose">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'close'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="agentClose" name="Agent Closes Complaint" flowable:delegateExpression="${closeComplaintDelegate}"/>
    <serviceTask id="sendClientNotification" name="Send Client Notification" flowable:delegateExpression="${sendClientNotificationDelegate}"/>
    <endEvent id="endAfterAgentClose" name="Closed by Agent"/>

    <!-- Escalation Path: Notify for Escalation -->
    <sequenceFlow id="flow_escalate_condition" sourceRef="agentDecision" targetRef="notifyEscalation">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'escalate'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="notifyEscalation" name="Notify Escalation" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Escalation Response -->
    <intermediateCatchEvent id="signalEscalated" name="Wait for Escalation Response">
      <signalEventDefinition signalRef="escalatedSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch after Escalation -->
    <serviceTask id="fetchAfterEscalation" name="Fetch After Escalation" flowable:delegateExpression="${fetchAfterEscalationDelegate}"/>

    <!-- Agent Lead Close Path -->
    <serviceTask id="agentLeadClose" name="Agent Lead Closes Complaint" flowable:delegateExpression="${closeComplaintByAgentLeadDelegate}"/>
    <serviceTask id="sendClientNotificationAfterEscalation" name="Send Client Notification" flowable:delegateExpression="${sendClientNotificationDelegate}"/>
    <endEvent id="endByAgentLead" name="Closed by Agent Lead"/>
 
    <!-- Flow Links -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fetchComplaintDetails"/>
    <sequenceFlow id="flow2" sourceRef="fetchComplaintDetails" targetRef="notifyAgent"/>
    <sequenceFlow id="flow3" sourceRef="notifyAgent" targetRef="signalNotifyAgent"/>
    <sequenceFlow id="flow4" sourceRef="signalNotifyAgent" targetRef="fetchAfterSignal"/>
    <sequenceFlow id="flow5" sourceRef="fetchAfterSignal" targetRef="agentDecision"/>
    <sequenceFlow id="flow6" sourceRef="agentClose" targetRef="sendClientNotification"/>
    <sequenceFlow id="flow6a" sourceRef="sendClientNotification" targetRef="endAfterAgentClose"/>
    <sequenceFlow id="flow7" sourceRef="notifyEscalation" targetRef="signalEscalated"/>
    <sequenceFlow id="flow8" sourceRef="signalEscalated" targetRef="fetchAfterEscalation"/>
    <sequenceFlow id="flow9" sourceRef="fetchAfterEscalation" targetRef="agentLeadClose"/>
    <sequenceFlow id="flow10" sourceRef="agentLeadClose" targetRef="sendClientNotificationAfterEscalation"/>
    <sequenceFlow id="flow10a" sourceRef="sendClientNotificationAfterEscalation" targetRef="endByAgentLead"/>
 
  </process> 
</definitions>
