<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:flowable="http://flowable.org/bpmn"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef">

  <signal id="notifyAgentSignal" name="NotifyAgent"/>
  <signal id="escalatedSignal" name="Escalated"/>

  <process id="complaintLifecycleProcess" name="Complaint Lifecycle Flow" isExecutable="true">

    <!-- Start -->
    <startEvent id="startEvent" name="Complaint Raised"/>

    <!-- Fetch Complaint Details -->
    <serviceTask id="fetchComplaintDetails" name="Fetch Complaint Details" flowable:delegateExpression="${fetchComplaintDelegate}"/>

    <!-- Notify Agent -->
    <serviceTask id="notifyAgent" name="Notify Agent" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Agent Response -->
    <intermediateCatchEvent id="signalNotifyAgent" name="Wait for Agent Response">
      <signalEventDefinition signalRef="notifyAgentSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch After Signal -->
    <serviceTask id="fetchAfterSignal" name="Fetch After Signal" flowable:delegateExpression="${fetchAfterSignalDelegate}"/>

    <!-- Agent Decision Gateway -->
    <exclusiveGateway id="agentDecision" name="Agent Decision"/>
 
    <!-- Close Path -->
    <sequenceFlow id="flow_close_condition" sourceRef="agentDecision" targetRef="agentClose">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'close'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="agentClose" name="Agent Closes Complaint" flowable:delegateExpression="${closeComplaintDelegate}"/>
    <serviceTask id="agentCommunicateAfterClose" name="Agent Communicates with Client" flowable:delegateExpression="${chatWithClientDelegate}"/>
    <endEvent id="endAfterAgentClose" name="Closed by Agent"/>

    <!-- Chat with Client Path -->
    <sequenceFlow id="flow_chat_with_client" sourceRef="agentDecision" targetRef="chatWithClient">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'chat'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="chatWithClient" name="Chat With Client" flowable:delegateExpression="${chatWithClientDelegate}"/>
    <endEvent id="endAfterChat" name="Closed after Chat"/>

    <!-- Escalation Path: Notify for Escalation -->
    <sequenceFlow id="flow_escalate_condition" sourceRef="agentDecision" targetRef="notifyEscalation">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'escalate'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="notifyEscalation" name="Notify Escalation" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Escalation Response -->
    <intermediateCatchEvent id="signalEscalated" name="Wait for Escalation Response">
      <signalEventDefinition signalRef="escalatedSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch after Escalation -->
    <serviceTask id="fetchAfterEscalation" name="Fetch After Escalation" flowable:delegateExpression="${fetchAfterEscalationDelegate}"/>

    <!-- Condition after escalation -->
    <exclusiveGateway id="checkEscalatedCondition" name="Check Escalated Condition"/>

    <!-- Agent Lead Close Path -->
    <sequenceFlow id="flow_escalated_close" sourceRef="checkEscalatedCondition" targetRef="agentLeadClose">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${escalationDecision == 'close'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="agentLeadClose" name="Agent Lead Closes Complaint" flowable:delegateExpression="${closeComplaintByAgentLeadDelegate}"/>
    <serviceTask id="agentCommunicateAfterEscalatedClose" name="Agent Communicates with Client" flowable:delegateExpression="${chatWithClientDelegate}"/>
    <endEvent id="endByAgentLead" name="Closed by Agent Lead"/>

    <!-- Escalated Chat Path -->
    <sequenceFlow id="flow_escalated_chat" sourceRef="checkEscalatedCondition" targetRef="chatWithClient">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${escalationDecision == 'chat'}]]></conditionExpression>
    </sequenceFlow>
 
    <!-- Flow Links -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fetchComplaintDetails"/>
    <sequenceFlow id="flow2" sourceRef="fetchComplaintDetails" targetRef="notifyAgent"/>
    <sequenceFlow id="flow3" sourceRef="notifyAgent" targetRef="signalNotifyAgent"/>
    <sequenceFlow id="flow4" sourceRef="signalNotifyAgent" targetRef="fetchAfterSignal"/>
    <sequenceFlow id="flow5" sourceRef="fetchAfterSignal" targetRef="agentDecision"/>
    <sequenceFlow id="flow6" sourceRef="agentClose" targetRef="agentCommunicateAfterClose"/>
    <sequenceFlow id="flow6a" sourceRef="agentCommunicateAfterClose" targetRef="endAfterAgentClose"/>
    <sequenceFlow id="flow7" sourceRef="notifyEscalation" targetRef="signalEscalated"/>
    <sequenceFlow id="flow8" sourceRef="signalEscalated" targetRef="fetchAfterEscalation"/>
    <sequenceFlow id="flow9" sourceRef="fetchAfterEscalation" targetRef="checkEscalatedCondition"/>
    <sequenceFlow id="flow10" sourceRef="agentLeadClose" targetRef="agentCommunicateAfterEscalatedClose"/>
    <sequenceFlow id="flow10a" sourceRef="agentCommunicateAfterEscalatedClose" targetRef="endByAgentLead"/>
    <sequenceFlow id="flow11" sourceRef="chatWithClient" targetRef="endAfterChat"/>
 
  </process> 
</definitions>
