<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:flowable="http://flowable.org/bpmn"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.flowable.org/processdef">

  <signal id="notifyAgentSignal" name="NotifyAgent"/>
  <signal id="escalatedSignal" name="Escalated"/>

  <process id="complaintLifecycleProcess" name="Complaint Lifecycle Flow" isExecutable="true">

    <!-- Start -->
    <startEvent id="startEvent" name="Complaint Raised"/>

    <!-- Fetch Complaint Details -->
    <serviceTask id="fetchComplaintDetails" name="Fetch Complaint Details" flowable:delegateExpression="${fetchComplaintDelegate}"/>

    <!-- Notify Agent -->
    <serviceTask id="notifyAgent" name="Notify Agent" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Agent Response -->
    <intermediateCatchEvent id="signalNotifyAgent" name="Wait for Agent Response">
      <signalEventDefinition signalRef="notifyAgentSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch After Signal -->
    <serviceTask id="fetchAfterSignal" name="Fetch After Signal" flowable:delegateExpression="${fetchComplaintDelegate}"/>

    <!-- Agent Decision Gateway -->
    <exclusiveGateway id="agentDecision" name="Agent Decision"/>
 
    <!-- Close Path --> 
    <sequenceFlow id="flow_close_condition" sourceRef="agentDecision" targetRef="agentClose"> 
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'close'}]]></conditionExpression> 
    </sequenceFlow> 
    <serviceTask id="agentClose" name="Agent Closes Complaint" flowable:delegateExpression="${closeComplaintDelegate}"/> 
 
    <!-- Agent Closes and Then Chats With Client --> 
    <serviceTask id="agentChatAfterClose" name="Agent Chats After Closing" flowable:delegateExpression="${agentChatAfterCloseDelegate}"/> 
    <endEvent id="endAfterAgentCloseAndChat" name="Closed After Agent Close and Chat"/> 
 
    <!-- Agent Chats with Client Only -->
    <sequenceFlow id="flow_chat_with_client" sourceRef="agentDecision" targetRef="agentChatWithClient">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'chat'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="agentChatWithClient" name="Agent Chats With Client" flowable:delegateExpression="${agentChatWithClientDelegate}"/>
    <endEvent id="endAfterAgentChat" name="Closed After Agent Chat"/>

    <!-- Escalation Path: Notify for Escalation -->
    <sequenceFlow id="flow_escalate_condition" sourceRef="agentDecision" targetRef="notifyEscalation">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${agentDecision == 'escalate'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="notifyEscalation" name="Notify Escalation" flowable:delegateExpression="${notifyAgentDelegate}"/>

    <!-- Intermediate Signal: Wait for Escalation Response -->
    <intermediateCatchEvent id="signalEscalated" name="Wait for Escalation Response">
      <signalEventDefinition signalRef="escalatedSignal"/>
    </intermediateCatchEvent>

    <!-- Fetch after Escalation -->
    <serviceTask id="fetchAfterEscalation" name="Fetch After Escalation" flowable:delegateExpression="${fetchAfterEscalationDelegate}"/>
 
    <!-- Condition after escalation --> 
    <exclusiveGateway id="checkEscalatedCondition" name="Check Escalated Condition"/> 
 
    <!-- If still close --> 
    <sequenceFlow id="flow_escalated_close" sourceRef="checkEscalatedCondition" targetRef="agentLeadClose"> 
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${escalationDecision == 'close'}]]></conditionExpression> 
    </sequenceFlow> 
    <serviceTask id="agentLeadClose" name="Agent Lead Closes Complaint" flowable:delegateExpression="${closeComplaintByAgentLeadDelegate}"/> 
    <endEvent id="endByAgentLead" name="Closed by Agent Lead"/> 
 
    <!-- If chat needed --> 
    <sequenceFlow id="flow_chat_needed" sourceRef="checkEscalatedCondition" targetRef="chatWithClient"> 
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${escalationDecision == 'chat'}]]></conditionExpression> 
    </sequenceFlow> 
    <serviceTask id="chatWithClient" name="Chat With Client" flowable:delegateExpression="${chatWithClientDelegate}"/> 
    <endEvent id="endAfterChat" name="Closed after Chat"/> 
 
    <!-- Flow Links -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fetchComplaintDetails"/>
    <sequenceFlow id="flow2" sourceRef="fetchComplaintDetails" targetRef="notifyAgent"/>
    <sequenceFlow id="flow3" sourceRef="notifyAgent" targetRef="signalNotifyAgent"/>
    <sequenceFlow id="flow4" sourceRef="signalNotifyAgent" targetRef="fetchAfterSignal"/>
    <sequenceFlow id="flow5" sourceRef="fetchAfterSignal" targetRef="agentDecision"/>
    <sequenceFlow id="flow6" sourceRef="agentClose" targetRef="agentChatAfterClose"/>
    <sequenceFlow id="flow7" sourceRef="agentChatAfterClose" targetRef="endAfterAgentCloseAndChat"/>
    <sequenceFlow id="flow8" sourceRef="notifyEscalation" targetRef="signalEscalated"/>
    <sequenceFlow id="flow9" sourceRef="signalEscalated" targetRef="fetchAfterEscalation"/>
    <sequenceFlow id="flow10" sourceRef="fetchAfterEscalation" targetRef="checkEscalatedCondition"/>
    <sequenceFlow id="flow11" sourceRef="agentLeadClose" targetRef="endByAgentLead"/>
    <sequenceFlow id="flow12" sourceRef="chatWithClient" targetRef="endAfterChat"/>
    <sequenceFlow id="flow13" sourceRef="agentChatWithClient" targetRef="endAfterAgentChat"/>
 
  </process> 
</definitions>
