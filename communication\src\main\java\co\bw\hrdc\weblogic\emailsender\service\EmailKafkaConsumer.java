package co.bw.hrdc.weblogic.emailsender.service;

import co.bw.hrdc.weblogic.emailsender.dto.EmailRequest;
import co.bw.hrdc.weblogic.emailsender.dto.NotificationDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class EmailKafkaConsumer {

    private static final Logger logger = LoggerFactory.getLogger(EmailKafkaConsumer.class);

    private final EmailService emailService;
    private final ObjectMapper objectMapper;

    public EmailKafkaConsumer(EmailService emailService) {
        this.emailService = emailService;
        this.objectMapper = new ObjectMapper();
    }

    @KafkaListener(topics = "sms-email-notificatioan", groupId = "email-service-group")
    public void otpListener(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value());
        emailService.sendEmail(record.value());
    }

    @KafkaListener(topics = "account-activation", groupId = "email-service-group")
    public void accountActivationListener(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value());
        emailService.sendEmail(record.value());
    }

    /**
     * Consumes notification messages from the notifications topic
     * Handles both EMAIL and IN_APP notification types from workflow integration
     */
    @KafkaListener(topics = "notifications", groupId = "email-service-group")
    public void notificationListener(ConsumerRecord<String, String> record) {
        logger.info("Received notification message from topic: {}, partition: {}, offset: {}",
                   record.topic(), record.partition(), record.offset());

        try {
            // Parse NotificationDTO from JSON
            NotificationDTO notification = objectMapper.readValue(record.value(), NotificationDTO.class);

            logger.info("Processing notification for recipient: {}, type: {}, subject: {}",
                       notification.getRecipient(), notification.getType(), notification.getSubject());

            // Handle EMAIL notifications
            if ("EMAIL".equals(notification.getType())) {
                handleEmailNotification(notification);
            }

            // Handle IN_APP notifications
            if ("IN_APP".equals(notification.getType())) {
                handleInAppNotification(notification);
            }

            logger.info("Successfully processed notification for recipient: {}", notification.getRecipient());

        } catch (Exception e) {
            logger.error("Error processing notification message: {}", e.getMessage(), e);
            logger.error("Failed message content: {}", record.value());
        }
    }

    /**
     * Handles EMAIL type notifications by sending emails
     */
    private void handleEmailNotification(NotificationDTO notification) {
        try {
            logger.info("Sending email notification to: {}", notification.getRecipient());

            // Create EmailRequest from NotificationDTO
            EmailRequest emailRequest = EmailRequest.builder()
                .contactAddress(notification.getRecipient())
                .name(notification.getName())
                .subject(notification.getSubject())
                .body(notification.getMessage())
                .build();

            // Convert to JSON and send via email service
            String emailJson = objectMapper.writeValueAsString(emailRequest);
            emailService.sendEmail(emailJson);

            logger.info("Email notification sent successfully to: {}", notification.getRecipient());

        } catch (Exception e) {
            logger.error("Error sending email notification to {}: {}", notification.getRecipient(), e.getMessage(), e);
        }
    }

    /**
     * Handles IN_APP type notifications
     * Currently logs the notification - can be extended to store in database or send to WebSocket
     */
    private void handleInAppNotification(NotificationDTO notification) {
        try {
            logger.info("Processing in-app notification for user: {}", notification.getRecipient());
            logger.info("Notification details - Subject: {}, Message: {}, Application Type: {}",
                       notification.getSubject(), notification.getMessage(), notification.getApplicationType());

            // TODO: Implement in-app notification handling
            // Options:
            // 1. Store in notification database
            // 2. Send to WebSocket for real-time UI updates
            // 3. Forward to notification service API

            // For now, just log the notification
            logger.info("In-app notification logged for user: {} - Subject: {}",
                       notification.getRecipient(), notification.getSubject());

        } catch (Exception e) {
            logger.error("Error processing in-app notification for {}: {}", notification.getRecipient(), e.getMessage(), e);
        }
    }
}
