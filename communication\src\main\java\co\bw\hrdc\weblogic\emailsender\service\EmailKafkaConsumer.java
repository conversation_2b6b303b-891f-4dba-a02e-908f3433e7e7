package co.bw.hrdc.weblogic.emailsender.service;

import co.bw.hrdc.weblogic.emailsender.dto.EmailRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class EmailKafkaConsumer {
    private final EmailService emailService;

    public EmailKafkaConsumer(EmailService emailService) {
        this.emailService = emailService;
    }

    @KafkaListener(topics = "sms-email-notificatioan", groupId = "email-service-group")
    public void otpListener(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value());
        emailService.sendEmail(record.value());
    }

    @KafkaListener(topics = "account-activation", groupId = "email-service-group")
    public void accountActivationListener(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value());
        emailService.sendEmail(record.value());
    }
}
