# Workflow API Testing Guide

## Available Complaint Workflow Endpoints

Based on the controller analysis, here are the correct endpoints:

### 1. Start Complaint Lifecycle (Recommended)
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/start/{complaintId}
```

### 2. Alternative Start Endpoints
```bash
# Legacy endpoint
POST http://localhost:8082/api/v1/workflow/start-complaint-lifecycle/{complaintId}

# Process complaints endpoint  
POST http://localhost:8082/api/v1/workflow/startprocess-compaints/{complaintId}
```

### 3. Resume/Signal Endpoints
```bash
# New signal endpoint
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}

# Legacy resume endpoint
POST http://localhost:8082/api/v1/workflow/resume-complaint-lifecycle/{processInstanceId}/{signalType}
```

### 4. Status Endpoint
```bash
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

## Testing Steps

### Step 1: Start the Workflow
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/start/cc610b03-df26-410b-8623-236e47afdc6c
```

**Expected Response (Service Available):**
```json
{
  "success": true,
  "message": "Complaint lifecycle process started successfully",
  "processInstanceId": "12345-67890-abcdef",
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c"
}
```

**Expected Response (Service Unavailable - Fallback):**
```json
{
  "success": true,
  "message": "Complaint lifecycle process started successfully",
  "processInstanceId": "12345-67890-abcdef",
  "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c"
}
```

### Step 2: Send Agent Signal
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
Content-Type: application/json

{
  "signalName": "NotifyAgent"
}
```

### Step 3: Check Status
```bash
GET http://localhost:8082/api/v1/workflow/complaint-lifecycle/status/{processInstanceId}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Complaint lifecycle status retrieved successfully",
  "processInstanceId": "12345-67890-abcdef",
  "processDefinitionKey": "complaintLifecycleProcess",
  "activeActivities": ["agentDecision"],
  "variables": {
    "complaintId": "cc610b03-df26-410b-8623-236e47afdc6c",
    "complaintData": {...},
    "agentDecision": "chat"
  },
  "isEnded": false
}
```

### Step 4: Send Escalation Signal (if needed)
```bash
POST http://localhost:8082/api/v1/workflow/complaint-lifecycle/signal/{processInstanceId}
Content-Type: application/json

{
  "signalName": "Escalated"
}
```

## Fallback Behavior

### When Workplace Learning Service is Unavailable:

1. **FetchComplaintDelegate** creates fallback data:
```json
{
  "id": "cc610b03-df26-410b-8623-236e47afdc6c",
  "status": "OPEN",
  "state": "SUBMITTED",
  "assignee": "SYSTEM",
  "typeOfComplaint": "GENERAL",
  "priority": "MEDIUM",
  "department": "GENERAL",
  "message": "Service unavailable - using fallback data"
}
```

2. **All API calls** return fallback responses:
```json
{
  "status": false,
  "message": "Workplace Learning service is currently unavailable",
  "data": null,
  "errors": null
}
```

3. **Workflow continues** with default decisions:
   - Agent Decision: "chat" (default)
   - Escalation Decision: "chat" (default)

## Troubleshooting

### Issue: 404 Not Found
**Cause:** Missing `/api/v1/workflow/` prefix
**Solution:** Use full path: `http://localhost:8082/api/v1/workflow/complaint-lifecycle/start/{id}`

### Issue: Service Unavailable
**Cause:** Workplace Learning service is down
**Behavior:** Workflow uses fallback data and continues
**Check:** Look for `serviceUnavailable: true` in process variables

### Issue: Process Stuck
**Cause:** Waiting for signal
**Solution:** Send appropriate signal:
- After start: `{"signalName": "NotifyAgent"}`
- After escalation: `{"signalName": "Escalated"}`

## Delegate Behavior with Fallbacks

### FetchComplaintDelegate
- **Success:** Fetches real complaint data
- **Fallback:** Creates mock complaint with default values
- **Variables Set:** complaintData, complaintStatus, complaintState, assignee

### FetchAfterSignalDelegate  
- **Success:** Updates complaint data and determines agent decision
- **Fallback:** Uses existing data or creates fallback, defaults to "chat"
- **Variables Set:** agentDecision (close/chat/escalate)

### NotifyAgentDelegate
- **Success:** Sends notifications to agents
- **Fallback:** Logs notification attempt, continues workflow
- **Variables Set:** agentNotified, agentLeadsNotified

### CloseComplaintDelegate
- **Success:** Closes complaint in system
- **Fallback:** Logs closure attempt, continues workflow
- **Variables Set:** closureSuccess, closedBy

### ChatWithClientDelegate
- **Success:** Adds communication and closes complaint
- **Fallback:** Logs communication attempt, continues workflow
- **Variables Set:** communicationSuccess, resolutionMethod

## Expected Workflow Flow

1. **Start** → Fetches complaint (fallback if needed)
2. **Notify** → Alerts agents (fallback if needed)  
3. **Signal Wait** → Waits for NotifyAgent signal
4. **Fetch After Signal** → Updates data, determines decision
5. **Gateway** → Routes based on decision:
   - **close** → Agent closes → End
   - **chat** → Chat with client → End
   - **escalate** → Notify escalation → Signal wait → Fetch → Gateway → (close/chat) → End

The workflow is designed to be resilient and continue even when the workplace learning service is unavailable.
