eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: "DMS"
    description: "HRDC Document Management System Application"
    version: "1.0.0"

app:
  logging:
    level: ERROR

server:
  contextPath: /hrdc
  port: 8092

spring:
  application:
    name: Document Management System
  jackson:
    serialization:
      INDENT_OUTPUT: true
  datasource:
    url: ************************************
    username: dms
    password: dms
    driverClassName: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
  data:
    jpa:
      repositories:
        enabled: true
  liquibase:
    enabled: true
    drop-first: true
    change-log: classpath:/db/changelog/db.changelog-master.xml

logging:
  level:
    bw.co.farmconnecta: ${app.logging.level}
    com.brahalla: ${app.logging.level}
    org.springframework: ${app.logging.level}
    org.hibernate: ${app.logging.level}

error:
  whitelabel:
    enabled: true

security:
  token:
    header: X-Auth-Token
    secret: ssssshhhhhhhhh!
    expiration: 86400000

farmconnector:
  test:
    data:
      enable: true

hibernate:
  format_sql: false
  dialect: org.hibernate.dialect.PostgreSQLDialect

liquibase:
  url: ${spring.datasource.url}
  user: ${spring.datasource.username}
  password: ${spring.datasource.password}

# Paperless-ngx API configuration
paperless:
  api:
    url: "http://192.81.214.119:8000"
    token: "9b222beb989bdb2cbf920a5d60cc4e8c1d93edf4"
    auth:
      username: "paperless"
      password: "paperless@123"

