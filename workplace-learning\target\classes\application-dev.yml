server:
  port: 8091

spring:
  application:
    name: workplace-learning
    profiles:
      active: local
  jackson:
    serialization:
      INDENT_OUTPUT: true
  datasource:
    url: ***************************************************
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
  data:
    jpa:
      repositories:
        enabled: true
  liquibase:
    enabled: false
    drop-first: true
    change-log: classpath:/db/changelog/db.changelog-master.xml

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB
  kafka:
    bootstrap-servers: localhost:9092
    template:
      default-topic: notifications    

jwt:
  auth:
    converter:
      resource-id: ${spring.security.oauth2.client.registration.keycloak.client-id}
      principal-attribute: principal_username

keycloak:
  server-url: https://hrdcdev.weblogic.co.bw/keycloak
  realm: hrdc
  client-id: ehrdf
  client-secret: 1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd
  admin-username: admin
  admin-password: admin
  protocol: http

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/
    healthcheck:
      enabled: true

feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
      account-service:
        url: http://localhost:3451  # Account Service URL
  circuitbreaker:
    enabled: true

logging:
  level:
    org.springframework:
      security: INFO
      web: INFO
    org:
      hibernate:
        SQL=DEBUG: DEBUG
  file:
    name: logs/workplace-learning-audit.log

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: "workplace-learning"
    description: "HRDC workplace learning Application"
    version: "1.0.0"

app:
  logging:
    level: "ERROR"

error:
  whitelabel:
    enabled: true

paperless:
  url: "http://**************:8000/api/documents"
  apiToken: "9b222beb989bdb2cbf920a5d60cc4e8c1d93edf4"