package bw.org.hrdc.weblogic.workplacelearning.util.converters;

import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanChangeRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @CreatedOn 06/06/25 10:03
 * @UpdatedBy thatosebape
 * @UpdatedOn 06/06/25 10:03
 */
@Converter
public class WorkPlaceTrainingPlanConverter implements AttributeConverter<Set<WorkPlaceTrainingPlanChangeRequest>, String> {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(Set<WorkPlaceTrainingPlanChangeRequest> attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting set to JSON", e);
        }
    }

    @Override
    public Set<WorkPlaceTrainingPlanChangeRequest> convertToEntityAttribute(String dbData) {
        try {
            JavaType type = objectMapper.getTypeFactory().constructCollectionType(Set.class, WorkPlaceTrainingPlanChangeRequest.class);
            return objectMapper.readValue(dbData, type);
        } catch (IOException e) {
            throw new RuntimeException("Error reading JSON from DB", e);
        }
    }
}
